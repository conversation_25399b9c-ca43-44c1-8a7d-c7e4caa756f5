import pandas as pd
from datetime import datetime
import os
import logger_config

logger = logger_config.setup_logger('main_logger', 'main.log')
 
# --- Load VIX data with headers ---
vix_df = pd.read_parquet("INDIA VIX_minute.parquet")  # Header is present
# Convert 'date' column (e.g., "09-01-2015 09:15") to datetime
vix_df['datetime'] = pd.to_datetime(vix_df['date'], format='%d-%m-%Y %H:%M')

def get_threshold(vix, fixed_threshold=None):
    """
    Calculate the threshold based on VIX value.

    VIX,Threshold - (10,0.3), (11,0.32), (12,0.34), (13,0.36), (14,0.38), (15,0.4), (16,0.41), (17,0.41), (18,0.42), (19,0.42), (20,0.43), (21,0.43), (22,0.44)

    """
    if(fixed_threshold is not None):
        threshold = fixed_threshold
        logger.info(f"Fixed Threshold:{threshold}")
        return round(threshold, 2)
    else :
        if(vix<=9):
            threshold = 0.28
        elif(vix>9 and vix<23):
            threshold = 0.3 + 0.02 * (vix - 10)
        else:
            threshold = 0.45
            
        Diff = float(os.getenv("Diff"))
        logger.info(f"Diff:{Diff}")    
        threshold = threshold - Diff
        logger.info(f"Threshold after diff:{threshold}")
    return round(threshold, 2)

def get_vix(target_date,start_time):
   
    # Combine date and start time into datetime
    target_datetime = pd.to_datetime(f"{target_date} {start_time}")

    # --- Lookup VIX close value at the exact datetime ---
    vix_row = vix_df[vix_df['datetime'] == target_datetime]

    if not vix_row.empty:
        vix_close = vix_row.iloc[0]['close']
        logger.info(f"VIX close at {target_datetime} is {vix_close}")
    else:
        logger.info(f"No VIX data found for {target_datetime}")
        return None
    return vix_close