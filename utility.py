import os
import pandas as pd
import logger_config
import math

logger = logger_config.setup_logger('main_logger', 'main.log')

# Determine sell option prices based on VIX
vix_price_map = {
    9: 14, 10: 15, 11: 16, 12:17, 13: 18, 14: 19, 15:20, 16: 20,
    17: 21, 18: 21, 19: 22, 20: 22, 21: 23
}

def round_vix(vix):
    """Round VIX value: 13.5 and above rounds up, below 13.5 rounds down."""
    return math.ceil(vix) if vix % 1 >= 0.5 else math.floor(vix)

def get_sell_option_price(vix):
    vix = round_vix(vix)
    logger.info(f"Rounded off vix close value : {vix}")
    #price = 35
    if vix <=8:
        price = 12
    elif vix >= 22:
        price = 24
    else:
        price = vix_price_map.get(vix) # Use vix_price_map for vix values between 9 and 21
    return float(price)

def get_calender_diff_value(price, vix):
    vix = round_vix(vix)
    if vix <= 13:
        diff = 10
    elif 14 <= vix <= 15:
        diff = 8
    elif 16 <= vix <= 18:
        diff = 5
    elif 19 <= vix <= 21:
        diff = 0
    elif vix >= 22:
        diff = -5
    return price + diff

def find_closest_option(df, target_date, target_price, start_time, symbol):
    """
    Finds the row in the DataFrame whose 'Close' is closest to the target price
    for the specified date and start_time.

    Returns (symbol, selected_row) or (None, None) if no matching row found.
    """
    # Ensure string formats for filtering
    target_date = str(target_date)
    start_time_str = start_time.strftime("%H:%M")

    # Ensure required columns exist
    required_cols = {"YMD", "Time", "Close"}
    if not required_cols.issubset(df.columns):
        return None, None

    df = df.copy()

    # Filter by date and time
    df = df[
        (df["YMD"].astype(str) == target_date) &
        (df["Time"].astype(str) == start_time_str)
    ]

    if df.empty:
        return None, None

    # Convert 'Close' to numeric and drop invalid rows
    df["Close"] = pd.to_numeric(df["Close"], errors="coerce")
    df = df.dropna(subset=["Close"])

    if df.empty:
        return None, None

    # Compute absolute difference from target_price
    df["diff"] = (df["Close"] - target_price).abs()

    # Find row with the smallest difference
    selected_row = df.loc[df["diff"].idxmin()]

    # Extract the symbol from the source_file column (filename without extension)
    if 'source_file' in selected_row:
        actual_symbol = selected_row['source_file']
    else:
        actual_symbol = symbol  # fallback to the passed symbol

    return actual_symbol, selected_row
   