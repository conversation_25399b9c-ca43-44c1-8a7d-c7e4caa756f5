// qtextcursor.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextCursor
{
%TypeHeaderCode
#include <qtextcursor.h>
%End

public:
    QTextCursor();
    explicit QTextCursor(QTextDocument *document);
    explicit QTextCursor(QTextFrame *frame);
    explicit QTextCursor(const QTextBlock &block);
    QTextCursor(const QTextCursor &cursor);
    ~QTextCursor();
    bool isNull() const;

    enum MoveMode
    {
        MoveAnchor,
        KeepAnchor,
    };

    void setPosition(int pos, QTextCursor::MoveMode mode = QTextCursor::MoveAnchor);
    int position() const;
    int anchor() const;
    void insertText(const QString &text);
    void insertText(const QString &text, const QTextCharFormat &format);

    enum MoveOperation
    {
        NoMove,
        Start,
        Up,
        StartOfLine,
        StartOfBlock,
        StartOfWord,
        PreviousBlock,
        PreviousCharacter,
        PreviousWord,
        Left,
        WordLeft,
        End,
        Down,
        EndOfLine,
        EndOfWord,
        EndOfBlock,
        NextBlock,
        NextCharacter,
        NextWord,
        Right,
        WordRight,
        NextCell,
        PreviousCell,
        NextRow,
        PreviousRow,
    };

    bool movePosition(QTextCursor::MoveOperation op, QTextCursor::MoveMode mode = QTextCursor::MoveAnchor, int n = 1);
    void deleteChar();
    void deletePreviousChar();

    enum SelectionType
    {
        WordUnderCursor,
        LineUnderCursor,
        BlockUnderCursor,
        Document,
    };

    void select(QTextCursor::SelectionType selection);
    bool hasSelection() const;
    bool hasComplexSelection() const;
    void removeSelectedText();
    void clearSelection();
    int selectionStart() const;
    int selectionEnd() const;
    QString selectedText() const;
    QTextDocumentFragment selection() const;
    void selectedTableCells(int *firstRow, int *numRows, int *firstColumn, int *numColumns) const;
    QTextBlock block() const;
    QTextCharFormat charFormat() const;
    void setCharFormat(const QTextCharFormat &format);
    void mergeCharFormat(const QTextCharFormat &modifier);
    QTextBlockFormat blockFormat() const;
    void setBlockFormat(const QTextBlockFormat &format);
    void mergeBlockFormat(const QTextBlockFormat &modifier);
    QTextCharFormat blockCharFormat() const;
    void setBlockCharFormat(const QTextCharFormat &format);
    void mergeBlockCharFormat(const QTextCharFormat &modifier);
    bool atBlockStart() const;
    bool atBlockEnd() const;
    bool atStart() const;
    bool atEnd() const;
    void insertBlock();
    void insertBlock(const QTextBlockFormat &format);
    void insertBlock(const QTextBlockFormat &format, const QTextCharFormat &charFormat);
    QTextList *insertList(const QTextListFormat &format);
    QTextList *insertList(QTextListFormat::Style style);
    QTextList *createList(const QTextListFormat &format);
    QTextList *createList(QTextListFormat::Style style);
    QTextList *currentList() const;
    QTextTable *insertTable(int rows, int cols, const QTextTableFormat &format);
    QTextTable *insertTable(int rows, int cols);
    QTextTable *currentTable() const;
    QTextFrame *insertFrame(const QTextFrameFormat &format);
    QTextFrame *currentFrame() const;
    void insertFragment(const QTextDocumentFragment &fragment);
    void insertHtml(const QString &html);
    void insertImage(const QTextImageFormat &format);
    void insertImage(const QTextImageFormat &format, QTextFrameFormat::Position alignment);
    void insertImage(const QString &name);
    void insertImage(const QImage &image, const QString &name = QString());
    void beginEditBlock();
    void joinPreviousEditBlock();
    void endEditBlock();
    int blockNumber() const;
    int columnNumber() const;
    bool operator!=(const QTextCursor &rhs) const;
    bool operator<(const QTextCursor &rhs) const;
    bool operator<=(const QTextCursor &rhs) const;
    bool operator==(const QTextCursor &rhs) const;
    bool operator>=(const QTextCursor &rhs) const;
    bool operator>(const QTextCursor &rhs) const;
    bool isCopyOf(const QTextCursor &other) const;
    bool visualNavigation() const;
    void setVisualNavigation(bool b);
    QTextDocument *document() const;
    int positionInBlock() const;
    void setVerticalMovementX(int x);
    int verticalMovementX() const;
    void setKeepPositionOnInsert(bool b);
    bool keepPositionOnInsert() const;
    void swap(QTextCursor &other /Constrained/);
};
