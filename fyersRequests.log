{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:09:52,035+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:10:40,053+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:10:58,452+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:11:09,241+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:11:18,737+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:11:47,371+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:12:39,605+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:13:19,402+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:14:57,318+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:16:20,657+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:17:28,750+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:19:05,893+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:19:32,981+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:19:59,109+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:20:24,685+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:21:09,172+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:21:25,324+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:21:32,790+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:21:39,580+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:26:04,443+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-11 15:27:33,409+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-11 15:28:06,523+0530","service":"FyersAPIRequest"}
