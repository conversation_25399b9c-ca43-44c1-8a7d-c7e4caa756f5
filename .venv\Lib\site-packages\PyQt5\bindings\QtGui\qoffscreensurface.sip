// qoffscreensurface.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QOffscreenSurface : QObject, QSurface
{
%TypeHeaderCode
#include <qoffscreensurface.h>
%End

public:
    explicit QOffscreenSurface(QScreen *screen = 0);
%If (Qt_5_10_0 -)
    QOffscreenSurface(QScreen *screen, QObject *parent /TransferThis/);
%End
    virtual ~QOffscreenSurface();
    virtual QSurface::SurfaceType surfaceType() const;
    void create();
    void destroy();
    bool isValid() const;
    void setFormat(const QSurfaceFormat &format);
    virtual QSurfaceFormat format() const;
    QSurfaceFormat requestedFormat() const;
    virtual QSize size() const;
    QScreen *screen() const;
    void setScreen(QScreen *screen);

signals:
    void screenChanged(QScreen *screen);

public:
%If (Qt_5_9_0 -)
    void *nativeHandle() const;
%End
%If (Qt_5_9_0 -)
    void setNativeHandle(void *handle);
%End
};

%End
