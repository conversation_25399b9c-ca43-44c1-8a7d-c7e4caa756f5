from fyers_apiv3 import fyersModel
import datetime
import time
import csv

# ---- Credentials ----
client_id = "O5MRPDE4TQ-100"  # Replace with your actual client_id
access_token ='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsiZDoxIiwiZDoyIiwieDowIiwieDoxIiwieDoyIl0sImF0X2hhc2giOiJnQUFBQUFCb2NJUm9HT2l4Z3Z3VExSVzJsdGxNNmN5YlJmNjljc2ZHVnpjUU9VemtkcDRkSnRSMXRqNDZTOGM1cVVRWURGUFIzOHI3OTc0N3hTMUttVXl0MnMtekFHRXJMeWo2V0liM2FGREpvMU1qemFUMThSQT0iLCJkaXNwbGF5X25hbWUiOiIiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiIyYjMyNzczM2IxZGYxNGUxNDBkOWUyZmRlMjU4MzhjYTM3OTVhMjcyM2ZkMmFiNTQzYjlhY2Q5NSIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWVkwMzQ0MCIsImFwcFR5cGUiOjEwMCwiZXhwIjoxNzUyMjgwMjAwLCJpYXQiOjE3NTIyMDQzOTIsImlzcyI6ImFwaS5meWVycy5pbiIsIm5iZiI6MTc1MjIwNDM5Miwic3ViIjoiYWNjZXNzX3Rva2VuIn0.9zkGv0xRlXef8jcOaC8dmS8KuEyj_y1BkvXp5XQmdPE'
 # Replace with your actual token

# ---- Initialize Fyers Model ----
fyers = fyersModel.FyersModel(
    client_id=client_id,
    is_async=False,
    token=access_token,
    log_path=""
)

# ---- Helper: Convert date string to epoch time ----
def to_unix(date_str):
    return int(time.mktime(datetime.datetime.strptime(date_str, "%Y-%m-%d").timetuple()))

# ---- Input Range ----
start_date = "2025-04-01"
end_date = "2025-04-30"
range_from = str(to_unix(start_date))
range_to = str(to_unix(end_date))

# ---- Request IndiaVIX Data ----
data = {
    "symbol": "NSE:INDIAVIX-INDEX",
    "resolution": "1",       # Use "1" for 1-minute data
    "date_format": "0",      # Timestamps as epoch seconds
    "range_from": range_from,
    "range_to": range_to,
    "cont_flag": "1"
}

response = fyers.history(data=data)

# ---- Save to CSV ----
if response.get("s") == "ok":
    candles = response["candles"]
    filename = "indiavix_data.csv"

    with open(filename, mode="w", newline="") as file:
        writer = csv.writer(file)
        writer.writerow(["date", "open", "high", "low", "close", "volume"])  # Header row

        for candle in candles:
            # Check if the candle data is complete
            if len(candle) < 6:
                print("⚠️ Incomplete data for a candle, skipping...")
                continue
            date_str = datetime.datetime.fromtimestamp(candle[0]).strftime('%Y-%m-%d %H:%M')
            writer.writerow([date_str, candle[1], candle[2], candle[3], candle[4], candle[5]])

    print(f"✅ Data saved to '{filename}'")

else:
    print("❌ Error fetching data:", response.get("message", response))
