import csv
import re

def extract_bcs_positions_from_log(log_file):
    """
    Extracts BCS positions from the log file.

    :param log_file: Path to the log file
    :return: List of BCS positions dictionaries
    """
    bcs_positions_list = []
    with open(log_file, 'r') as file:
        for line in file:
            if "BCS_positions:" in line:
                # Extract the BCS positions dictionary from the log line
                match = re.search(r"BCS_positions: (.+)", line)
                if match:
                    bcs_positions_str = match.group(1)
                    bcs_positions = eval(bcs_positions_str)  # Convert string to dictionary
                    bcs_positions_list.append(bcs_positions)
    return bcs_positions_list

def generate_csv_from_log(log_file, output_file):
    """
    Generates a CSV file from BCS positions extracted from the log file.

    :param log_file: Path to the log file
    :param output_file: Path to the output CSV file
    """
    bcs_positions_list = extract_bcs_positions_from_log(log_file)
    csv_data = []

    for bcs_positions in bcs_positions_list:
        ce_sell = bcs_positions.get('CE_SELL', {})
        pe_sell = bcs_positions.get('PE_SELL', {})

        ce_sell_strike = extract_strike_price(ce_sell.get('symbol', ''))
        pe_sell_strike = extract_strike_price(pe_sell.get('symbol', ''))

        strike_range = abs(ce_sell_strike - pe_sell_strike)

        csv_data.append({
            'Date': ce_sell.get('Date', ''),
            'CE_SELL Symbol': ce_sell.get('symbol', ''),
            'PE_SELL Symbol': pe_sell.get('symbol', ''),
            'CE_SELL_STRIKE': ce_sell_strike,
            'PE_SELL_STRIKE': pe_sell_strike,
            'Range': strike_range
        })

    # Write to CSV
    with open(output_file, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=csv_data[0].keys())
        writer.writeheader()
        writer.writerows(csv_data)

def extract_strike_price(symbol):
    """
    Extracts the strike price from the option symbol.

    :param symbol: Option symbol string
    :return: Strike price as an integer
    """
    match = re.search(r'(\d{5})[CP]E', symbol)
    return int(match.group(1)) if match else 0

# Example usage
log_file = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\logs\July_2025\15-07-2025.log"

generate_csv_from_log(log_file, 'bcs_positions.csv')