import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "qqmlsettings_p.h"
        name: "QQmlSettings"
        prototype: "QObject"
        exports: ["Qt.labs.settings/Settings 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "category"; type: "string" }
        Property { name: "fileName"; type: "string" }
        Method { name: "_q_propertyChanged" }
        Method {
            name: "value"
            type: "QVariant"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "defaultValue"; type: "QVariant" }
        }
        Method {
            name: "value"
            type: "QVariant"
            Parameter { name: "key"; type: "string" }
        }
        Method {
            name: "setValue"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method { name: "sync" }
    }
}
