// qmediagaplessplaybackcontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaGaplessPlaybackControl : QMediaControl
{
%TypeHeaderCode
#include <qmediagaplessplaybackcontrol.h>
%End

public:
    virtual ~QMediaGaplessPlaybackControl();
    virtual QMediaContent nextMedia() const = 0;
    virtual void setNextMedia(const QMediaContent &media) = 0;
    virtual bool isCrossfadeSupported() const = 0;
    virtual qreal crossfadeTime() const = 0;
    virtual void setCrossfadeTime(qreal crossfadeTime) = 0;

signals:
    void crossfadeTimeChanged(qreal crossfadeTime);
    void nextMediaChanged(const QMediaContent &media);
    void advancedToNextMedia();

protected:
    explicit QMediaGaplessPlaybackControl(QObject *parent /TransferThis/ = 0);
};
