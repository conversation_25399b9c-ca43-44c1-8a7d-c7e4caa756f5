import pandas as pd

# Step 1: Load both CSV files
equity_df = pd.read_csv(r'C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\EC\equity_curve_15-07-2025.csv', sep=',')  # Ensure correct separator
strike_df = pd.read_csv(r'C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\EC\bcs_positions_15-07-2025.csv')

# Print column names to verify
print("Equity DataFrame Columns:", equity_df.columns)
print("Strike DataFrame Columns:", strike_df.columns)

# Strip column names to remove any leading/trailing spaces
equity_df.columns = equity_df.columns.str.strip()
strike_df.columns = strike_df.columns.str.strip()

# Check if 'Date' column exists
if 'Date' not in equity_df.columns:
    raise KeyError("The 'Date' column is missing in the equity DataFrame.")

# Drop unnamed columns
strike_df = strike_df.drop(columns=[col for col in strike_df.columns if "Unnamed" in col])

# Step 3: Convert both date columns to datetime format
equity_df['Date'] = pd.to_datetime(equity_df['Date'], format='%d-%m-%Y')
strike_df['Date'] = pd.to_datetime(strike_df['Date'].astype(str), format='%Y%m%d')

# Step 4: Merge on Date
merged_df = pd.merge(equity_df, strike_df[['Date', 'Range']], on='Date', how='left')

# Step 5: Convert Date back to original format if needed
merged_df['Date'] = merged_df['Date'].dt.strftime('%d-%m-%Y')

# Step 6: Save or display
merged_df.to_csv("merged_equity_with_range.csv", index=False)
print(merged_df.head())