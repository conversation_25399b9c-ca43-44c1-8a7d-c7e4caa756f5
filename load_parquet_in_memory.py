from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import logger_config
import re, csv, os, time
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

logger = logger_config.setup_logger('main_logger', 'main.log')


# Function to load Parquet files into memory with filename as DataFrame key
def load_parquet_data(folders):

    '''
    The keys at the top level are folder names.
    Each folder contains subfolders with keys CE_BUY, CE_SELL, PE_BUY, and PE_SELL.
    Each subfolder's value is another dictionary where the keys are the base file names (without extensions) of the.
    parquet files, and the values are pandas DataFrame objects resulting from loading those files.
    
    '''
    
    data = {}
    max_workers = max(1, int(os.cpu_count() * 0.95))
    logger.info(f"Using {max_workers} workers for parallel processing")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for folder in folders:
            data[folder] = {}
            futures = []
            file_paths = []

            for subfolder in ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']:
                subfolder_path = os.path.join(folder, subfolder)
                parquet_files = [f for f in os.listdir(subfolder_path) if f.endswith('.parquet')]

                for file in parquet_files:
                    file_path = os.path.join(subfolder_path, file)
                    futures.append(executor.submit(pd.read_parquet, file_path))
                    file_paths.append((subfolder, file_path))

            # Collect results
            for (subfolder, file_path), future in zip(file_paths, futures):
                if subfolder not in data[folder]:
                    data[folder][subfolder] = {}

                try:
                    df = future.result()
                    filename_key = os.path.splitext(os.path.basename(file_path))[0]
                    data[folder][subfolder][filename_key] = df
                except Exception as e:
                    logger.error(f"Error loading {file_path}: {e}")
    
    return data


# ------------------ Folder Filtering ------------------ #
def list_date_folders(root_path, from_date, to_date):
    date_folder_pattern = re.compile(r"^\d{8}$")
    all_entries = [
        os.path.join(root_path, name)
        for name in os.listdir(root_path)
        if date_folder_pattern.match(name) and os.path.isdir(os.path.join(root_path, name))
    ]
    all_entries.sort()
    filtered_entries = [
        f for f in all_entries
        if from_date <= datetime.strptime(os.path.basename(f), "%Y%m%d") <= to_date
    ]

    valid_folders = []
    skipped_dates = []

    for folder in filtered_entries:
        date_str = os.path.basename(folder)
        subfolders = ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']
        valid = all(
            os.path.exists(os.path.join(folder, sub)) and
            any(f.endswith('.parquet') for f in os.listdir(os.path.join(folder, sub)))
            for sub in subfolders
        )
        if valid:
            valid_folders.append(folder)
        else:
            skipped_dates.append(date_str)

    with open('skipped_folders.csv', mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Skipped_Folder_Date'])
        for date in skipped_dates:
            writer.writerow([date])

    print(f"Found {len(valid_folders)} valid folders between {from_date.date()} and {to_date.date()}")
    print(f"Skipped {len(skipped_dates)} folders due to missing or empty subfolders.")
    
    return valid_folders

