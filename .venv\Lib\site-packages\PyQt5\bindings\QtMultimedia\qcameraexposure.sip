// qcameraexposure.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraExposure : QObject
{
%TypeHeaderCode
#include <qcameraexposure.h>
%End

public:
    enum FlashMode
    {
        <PERSON>Auto,
        <PERSON>Off,
        FlashOn,
        FlashRedEyeReduction,
        FlashFill,
        FlashTorch,
        FlashVideoLight,
        FlashSlowSyncFrontCurtain,
        FlashSlowSyncRearCurtain,
        FlashManual,
    };

    typedef QFlags<QCameraExposure::FlashMode> FlashModes;

    enum ExposureMode
    {
        ExposureAuto,
        ExposureManual,
        ExposurePortrait,
        ExposureNight,
        ExposureBacklight,
        ExposureSpotlight,
        ExposureSports,
        ExposureSnow,
        ExposureBeach,
        ExposureLargeAperture,
        ExposureSmallAperture,
%If (Qt_5_5_0 -)
        ExposureAction,
%End
%If (Qt_5_5_0 -)
        ExposureLandscape,
%End
%If (Qt_5_5_0 -)
        ExposureNightPortrait,
%End
%If (Qt_5_5_0 -)
        ExposureTheatre,
%End
%If (Qt_5_5_0 -)
        ExposureSunset,
%End
%If (Qt_5_5_0 -)
        ExposureSteadyPhoto,
%End
%If (Qt_5_5_0 -)
        ExposureFireworks,
%End
%If (Qt_5_5_0 -)
        ExposureParty,
%End
%If (Qt_5_5_0 -)
        ExposureCandlelight,
%End
%If (Qt_5_5_0 -)
        ExposureBarcode,
%End
        ExposureModeVendor,
    };

    enum MeteringMode
    {
        MeteringMatrix,
        MeteringAverage,
        MeteringSpot,
    };

    bool isAvailable() const;
    QCameraExposure::FlashModes flashMode() const;
    bool isFlashModeSupported(QCameraExposure::FlashModes mode) const;
    bool isFlashReady() const;
    QCameraExposure::ExposureMode exposureMode() const;
    bool isExposureModeSupported(QCameraExposure::ExposureMode mode) const;
    qreal exposureCompensation() const;
    QCameraExposure::MeteringMode meteringMode() const;
    bool isMeteringModeSupported(QCameraExposure::MeteringMode mode) const;
    QPointF spotMeteringPoint() const;
    void setSpotMeteringPoint(const QPointF &point);
    int isoSensitivity() const;
    qreal aperture() const;
    qreal shutterSpeed() const;
    int requestedIsoSensitivity() const;
    qreal requestedAperture() const;
    qreal requestedShutterSpeed() const;
    QList<int> supportedIsoSensitivities(bool *continuous = 0) const;
    QList<qreal> supportedApertures(bool *continuous = 0) const;
    QList<qreal> supportedShutterSpeeds(bool *continuous = 0) const;

public slots:
    void setFlashMode(QCameraExposure::FlashModes mode);
    void setExposureMode(QCameraExposure::ExposureMode mode);
    void setMeteringMode(QCameraExposure::MeteringMode mode);
    void setExposureCompensation(qreal ev);
    void setManualIsoSensitivity(int iso);
    void setAutoIsoSensitivity();
    void setManualAperture(qreal aperture);
    void setAutoAperture();
    void setManualShutterSpeed(qreal seconds);
    void setAutoShutterSpeed();

signals:
    void flashReady(bool);
    void apertureChanged(qreal);
    void apertureRangeChanged();
    void shutterSpeedChanged(qreal);
    void shutterSpeedRangeChanged();
    void isoSensitivityChanged(int);
    void exposureCompensationChanged(qreal);

private:
    explicit QCameraExposure(QCamera *parent /TransferThis/ = 0);

protected:
%If (Qt_5_14_0 -)
    virtual ~QCameraExposure();
%End

private:
%If (- Qt_5_14_0)
    virtual ~QCameraExposure();
%End
};

QFlags<QCameraExposure::FlashMode> operator|(QCameraExposure::FlashMode f1, QFlags<QCameraExposure::FlashMode> f2);
