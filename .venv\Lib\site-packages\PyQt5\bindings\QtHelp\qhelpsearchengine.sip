// qhelpsearchengine.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpSearchQuery
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    enum FieldName
    {
        DEFAULT,
        FUZZY,
        WITHOUT,
        PHRASE,
        ALL,
        ATLEAST,
    };

    QHelpSearchQuery();
    QHelpSearchQuery(QHelpSearchQuery::FieldName field, const QStringList &wordList);
};

class QHelpSearchEngine : QObject
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    QHelpSearchEngine(QHelpEngineCore *helpEngine, QObject *parent /TransferThis/ = 0);
    virtual ~QHelpSearchEngine();
    QList<QHelpSearchQuery> query() const;
    QHelpSearchQueryWidget *queryWidget();
    QHelpSearchResultWidget *resultWidget();
    int hitCount() const;
    QList<QPair<QString, QString>> hits(int start, int end) const;

public slots:
    void reindexDocumentation();
    void cancelIndexing();
    void search(const QList<QHelpSearchQuery> &queryList);
    void cancelSearching();

signals:
    void indexingStarted();
    void indexingFinished();
    void searchingStarted();
    void searchingFinished(int hits);

public:
%If (Qt_5_9_0 -)
    int searchResultCount() const;
%End
%If (Qt_5_9_0 -)
    QVector<QHelpSearchResult> searchResults(int start, int end) const;
%End
%If (Qt_5_9_0 -)
    QString searchInput() const;
%End

public slots:
%If (Qt_5_9_0 -)
    void search(const QString &searchInput);
%End
};

%If (Qt_5_9_0 -)

class QHelpSearchResult
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    QHelpSearchResult();
    QHelpSearchResult(const QHelpSearchResult &other);
    QHelpSearchResult(const QUrl &url, const QString &title, const QString &snippet);
    ~QHelpSearchResult();
    QString title() const;
    QUrl url() const;
    QString snippet() const;
};

%End
