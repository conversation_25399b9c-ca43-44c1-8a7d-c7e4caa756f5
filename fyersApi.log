{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 366 days greater than range_from for resolutions 1D"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:10:40,053+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 366 days greater than range_from for resolutions 1D"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:10:58,452+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 366 days greater than range_from for resolutions 1D"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:11:09,241+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 366 days greater than range_from for resolutions 1D"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:11:18,737+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:17:28,750+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:19:05,893+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:19:32,981+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:19:59,109+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:20:24,685+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:21:09,172+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:21:25,324+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:21:32,790+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:26:04,443+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"range_to":"range_to cannot be 100 days greater than range_from for resolutions 1, 2, 3, 5, 10, 15, 20, 30, 45, 60, 120, 180 and 240"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-11 15:27:33,409+0530","service":"FyersAPI"}
