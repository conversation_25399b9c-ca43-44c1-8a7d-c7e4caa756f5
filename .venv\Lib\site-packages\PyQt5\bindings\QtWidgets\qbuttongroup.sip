// qbuttongroup.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QButtonGroup : QObject
{
%TypeHeaderCode
#include <qbuttongroup.h>
%End

public:
    explicit QButtonGroup(QObject *parent /TransferThis/ = 0);
    virtual ~QButtonGroup();
    void setExclusive(bool);
    bool exclusive() const;
    void addButton(QAbstractButton *, int id = -1);
    void removeButton(QAbstractButton *);
    QList<QAbstractButton *> buttons() const;
    QAbstractButton *button(int id) const;
    QAbstractButton *checkedButton() const;
    void setId(QAbstractButton *button, int id);
    int id(QAbstractButton *button) const;
    int checkedId() const;

signals:
    void buttonClicked(QAbstractButton *);
    void buttonClicked(int);
    void buttonPressed(QAbstractButton *);
    void buttonPressed(int);
    void buttonReleased(QAbstractButton *);
    void buttonReleased(int);
%If (Qt_5_2_0 -)
    void buttonToggled(QAbstractButton *, bool);
%End
%If (Qt_5_2_0 -)
    void buttonToggled(int, bool);
%End
%If (Qt_5_15_0 -)
    void idClicked(int);
%End
%If (Qt_5_15_0 -)
    void idPressed(int);
%End
%If (Qt_5_15_0 -)
    void idReleased(int);
%End
%If (Qt_5_15_0 -)
    void idToggled(int, bool);
%End
};
