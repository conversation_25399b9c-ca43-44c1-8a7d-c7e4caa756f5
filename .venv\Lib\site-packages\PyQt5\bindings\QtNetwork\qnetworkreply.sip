// qnetworkreply.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkReply : QIODevice
{
%TypeHeaderCode
#include <qnetworkreply.h>
%End

public:
    enum NetworkError
    {
        NoError,
        ConnectionRefusedError,
        RemoteHostClosedError,
        HostNotFoundError,
        TimeoutError,
        OperationCanceledError,
        SslHandshakeFailedError,
        UnknownNetworkError,
        ProxyConnectionRefusedError,
        ProxyConnectionClosedError,
        ProxyNotFoundError,
        ProxyTimeoutError,
        ProxyAuthenticationRequiredError,
        UnknownProxyError,
        ContentAccessDenied,
        ContentOperationNotPermittedError,
        ContentNotFoundError,
        AuthenticationRequiredError,
        UnknownContentError,
        ProtocolUnknownError,
        ProtocolInvalidOperationError,
        ProtocolFailure,
        ContentReSendError,
        TemporaryNetworkFailureError,
        NetworkSessionFailedError,
        BackgroundRequestNotAllowedError,
%If (Qt_5_3_0 -)
        ContentConflictError,
%End
%If (Qt_5_3_0 -)
        ContentGoneError,
%End
%If (Qt_5_3_0 -)
        InternalServerError,
%End
%If (Qt_5_3_0 -)
        OperationNotImplementedError,
%End
%If (Qt_5_3_0 -)
        ServiceUnavailableError,
%End
%If (Qt_5_3_0 -)
        UnknownServerError,
%End
%If (Qt_5_6_0 -)
        TooManyRedirectsError,
%End
%If (Qt_5_6_0 -)
        InsecureRedirectError,
%End
    };

    virtual ~QNetworkReply();
    virtual void abort() = 0;
    virtual void close();
    virtual bool isSequential() const;
    qint64 readBufferSize() const;
    virtual void setReadBufferSize(qint64 size);
    QNetworkAccessManager *manager() const;
    QNetworkAccessManager::Operation operation() const;
    QNetworkRequest request() const;
    QNetworkReply::NetworkError error() const;
    QUrl url() const;
    QVariant header(QNetworkRequest::KnownHeaders header) const;
    bool hasRawHeader(const QByteArray &headerName) const;
    QList<QByteArray> rawHeaderList() const;
    QByteArray rawHeader(const QByteArray &headerName) const;
    QVariant attribute(QNetworkRequest::Attribute code) const;
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &configuration);
%End

public slots:
    virtual void ignoreSslErrors();

signals:
    void metaDataChanged();
    void finished();
%If (Qt_5_1_0 -)
%If (PyQt_SSL)
    void encrypted();
%End
%End
    void error(QNetworkReply::NetworkError);
%If (Qt_5_15_0 -)
    void errorOccurred(QNetworkReply::NetworkError);
%End
%If (PyQt_SSL)
    void sslErrors(const QList<QSslError> &errors);
%End
    void uploadProgress(qint64 bytesSent, qint64 bytesTotal);
    void downloadProgress(qint64 bytesReceived, qint64 bytesTotal);
%If (Qt_5_5_0 -)
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End
%End
%If (Qt_5_6_0 -)
    void redirected(const QUrl &url);
%End
%If (Qt_5_9_0 -)
    void redirectAllowed();
%End

protected:
    explicit QNetworkReply(QObject *parent /TransferThis/ = 0);
    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;
    void setOperation(QNetworkAccessManager::Operation operation);
    void setRequest(const QNetworkRequest &request);
    void setError(QNetworkReply::NetworkError errorCode, const QString &errorString);
    void setUrl(const QUrl &url);
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
    void setRawHeader(const QByteArray &headerName, const QByteArray &value);
    void setAttribute(QNetworkRequest::Attribute code, const QVariant &value);
    void setFinished(bool finished);

public:
    bool isFinished() const;
    bool isRunning() const;
%If (PyQt_SSL)
    void ignoreSslErrors(const QList<QSslError> &errors);
%End
    const QList<QPair<QByteArray, QByteArray>> &rawHeaderPairs() const;

protected:
%If (PyQt_SSL)
    virtual void sslConfigurationImplementation(QSslConfiguration &) const;
%End
%If (PyQt_SSL)
    virtual void setSslConfigurationImplementation(const QSslConfiguration &);
%End
%If (PyQt_SSL)
    virtual void ignoreSslErrorsImplementation(const QList<QSslError> &);
%End
};
