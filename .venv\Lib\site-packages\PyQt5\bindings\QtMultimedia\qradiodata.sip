// qradiodata.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRadioData : QObject, QMediaBindableInterface
{
%TypeHeaderCode
#include <qradiodata.h>
%End

public:
    enum Error
    {
        NoError,
        ResourceError,
        OpenError,
        OutOfRangeError,
    };

    enum ProgramType
    {
        Undefined,
        News,
        CurrentAffairs,
        Information,
        Sport,
        Education,
        Drama,
        Culture,
        Science,
        Varied,
        PopMusic,
        RockMusic,
        EasyListening,
        LightClassical,
        SeriousClassical,
        OtherMusic,
        Weather,
        Finance,
        ChildrensProgrammes,
        SocialAffairs,
        Religion,
        PhoneIn,
        Travel,
        Leisure,
        JazzMusic,
        CountryMusic,
        NationalMusic,
        OldiesMusic,
        FolkMusic,
        Documentary,
        AlarmTest,
        Alarm,
        Talk,
        ClassicRock,
        AdultHits,
        SoftRock,
        Top40,
        Soft,
        Nostalgia,
        Classical,
        RhythmAndBlues,
        SoftRhythmAndBlues,
        Language,
        ReligiousMusic,
        ReligiousTalk,
        Personality,
        Public,
        College,
    };

    QRadioData(QMediaObject *mediaObject, QObject *parent /TransferThis/ = 0);
    virtual ~QRadioData();
    virtual QMediaObject *mediaObject() const;
    QMultimedia::AvailabilityStatus availability() const;
    QString stationId() const;
    QRadioData::ProgramType programType() const;
    QString programTypeName() const;
    QString stationName() const;
    QString radioText() const;
    bool isAlternativeFrequenciesEnabled() const;
    QRadioData::Error error() const;
    QString errorString() const;

public slots:
    void setAlternativeFrequenciesEnabled(bool enabled);

signals:
    void stationIdChanged(QString stationId);
    void programTypeChanged(QRadioData::ProgramType programType);
    void programTypeNameChanged(QString programTypeName);
    void stationNameChanged(QString stationName);
    void radioTextChanged(QString radioText);
    void alternativeFrequenciesEnabledChanged(bool enabled);
    void error(QRadioData::Error error);

protected:
    virtual bool setMediaObject(QMediaObject *);
};
