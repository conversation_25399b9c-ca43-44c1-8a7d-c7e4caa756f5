// qnetworkproxy.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkProxy
{
%TypeHeaderCode
#include <qnetworkproxy.h>
%End

public:
    enum ProxyType
    {
        DefaultProxy,
        Socks5Proxy,
        No<PERSON>ro<PERSON>,
        HttpProxy,
        HttpCachingProxy,
        FtpCachingProxy,
    };

    QNetworkProxy();
    QNetworkProxy(QNetworkProxy::ProxyType type, const QString &hostName = QString(), quint16 port = 0, const QString &user = QString(), const QString &password = QString());
    QNetworkProxy(const QNetworkProxy &other);
    ~QNetworkProxy();
    void setType(QNetworkProxy::ProxyType type);
    QNetworkProxy::ProxyType type() const;
    void setUser(const QString &userName);
    QString user() const;
    void setPassword(const QString &password);
    QString password() const;
    void setHostName(const QString &hostName);
    QString hostName() const;
    void setPort(quint16 port);
    quint16 port() const;
    static void setApplicationProxy(const QNetworkProxy &proxy);
    static QNetworkProxy applicationProxy();
    bool isCachingProxy() const;
    bool isTransparentProxy() const;
    bool operator==(const QNetworkProxy &other) const;
    bool operator!=(const QNetworkProxy &other) const;

    enum Capability
    {
        TunnelingCapability,
        ListeningCapability,
        UdpTunnelingCapability,
        CachingCapability,
        HostNameLookupCapability,
%If (Qt_5_8_0 -)
        SctpTunnelingCapability,
%End
%If (Qt_5_8_0 -)
        SctpListeningCapability,
%End
    };

    typedef QFlags<QNetworkProxy::Capability> Capabilities;
    void setCapabilities(QNetworkProxy::Capabilities capab);
    QNetworkProxy::Capabilities capabilities() const;
    void swap(QNetworkProxy &other /Constrained/);
    QVariant header(QNetworkRequest::KnownHeaders header) const;
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
    bool hasRawHeader(const QByteArray &headerName) const;
    QList<QByteArray> rawHeaderList() const;
    QByteArray rawHeader(const QByteArray &headerName) const;
    void setRawHeader(const QByteArray &headerName, const QByteArray &value);
};

class QNetworkProxyQuery
{
%TypeHeaderCode
#include <qnetworkproxy.h>
%End

public:
    enum QueryType
    {
        TcpSocket,
        UdpSocket,
        TcpServer,
        UrlRequest,
%If (Qt_5_8_0 -)
        SctpSocket,
%End
%If (Qt_5_8_0 -)
        SctpServer,
%End
    };

    QNetworkProxyQuery();
    QNetworkProxyQuery(const QUrl &requestUrl, QNetworkProxyQuery::QueryType type = QNetworkProxyQuery::UrlRequest);
    QNetworkProxyQuery(const QString &hostname, int port, const QString &protocolTag = QString(), QNetworkProxyQuery::QueryType type = QNetworkProxyQuery::TcpSocket);
    QNetworkProxyQuery(quint16 bindPort, const QString &protocolTag = QString(), QNetworkProxyQuery::QueryType type = QNetworkProxyQuery::TcpServer);
    QNetworkProxyQuery(const QNetworkConfiguration &networkConfiguration, const QUrl &requestUrl, QNetworkProxyQuery::QueryType queryType = QNetworkProxyQuery::UrlRequest);
    QNetworkProxyQuery(const QNetworkConfiguration &networkConfiguration, const QString &hostname, int port, const QString &protocolTag = QString(), QNetworkProxyQuery::QueryType type = QNetworkProxyQuery::TcpSocket);
    QNetworkProxyQuery(const QNetworkConfiguration &networkConfiguration, quint16 bindPort, const QString &protocolTag = QString(), QNetworkProxyQuery::QueryType type = QNetworkProxyQuery::TcpServer);
    QNetworkProxyQuery(const QNetworkProxyQuery &other);
    ~QNetworkProxyQuery();
    bool operator==(const QNetworkProxyQuery &other) const;
    bool operator!=(const QNetworkProxyQuery &other) const;
    QNetworkProxyQuery::QueryType queryType() const;
    void setQueryType(QNetworkProxyQuery::QueryType type);
    int peerPort() const;
    void setPeerPort(int port);
    QString peerHostName() const;
    void setPeerHostName(const QString &hostname);
    int localPort() const;
    void setLocalPort(int port);
    QString protocolTag() const;
    void setProtocolTag(const QString &protocolTag);
    QUrl url() const;
    void setUrl(const QUrl &url);
    QNetworkConfiguration networkConfiguration() const;
    void setNetworkConfiguration(const QNetworkConfiguration &networkConfiguration);
    void swap(QNetworkProxyQuery &other /Constrained/);
};

class QNetworkProxyFactory /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qnetworkproxy.h>
%End

public:
    QNetworkProxyFactory();
    virtual ~QNetworkProxyFactory();
    virtual QList<QNetworkProxy> queryProxy(const QNetworkProxyQuery &query = QNetworkProxyQuery()) = 0;
    static void setApplicationProxyFactory(QNetworkProxyFactory *factory /Transfer/);
    static QList<QNetworkProxy> proxyForQuery(const QNetworkProxyQuery &query);
    static QList<QNetworkProxy> systemProxyForQuery(const QNetworkProxyQuery &query = QNetworkProxyQuery());
    static void setUseSystemConfiguration(bool enable);
%If (Qt_5_8_0 -)
    static bool usesSystemConfiguration();
%End
};
