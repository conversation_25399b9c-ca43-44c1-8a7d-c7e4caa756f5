import os
import pandas as pd

def convert_csvs_to_parquet(base_folder, parquet_base_folder, subfolders=("CE_SELL", "PE_SELL", "CE_BUY", "PE_BUY")):
    for date_folder in os.listdir(base_folder):
        date_folder_path = os.path.join(base_folder, date_folder)
        parquet_date_folder_path = os.path.join(parquet_base_folder, date_folder)

        if not os.path.isdir(date_folder_path):
            continue

        for subfolder in subfolders:
            full_subfolder_path = os.path.join(date_folder_path, subfolder)
            parquet_subfolder_path = os.path.join(parquet_date_folder_path, subfolder)

            if not os.path.exists(full_subfolder_path):
                print(f"Subfolder not found: {full_subfolder_path}")
                continue

            # Create the corresponding subfolder in the Parquet base folder
            os.makedirs(parquet_subfolder_path, exist_ok=True)

            for filename in os.listdir(full_subfolder_path):
                if filename.endswith(".csv"):
                    csv_path = os.path.join(full_subfolder_path, filename)
                    parquet_path = os.path.join(parquet_subfolder_path, filename.replace(".csv", ".parquet"))

                    print(f"Converting {filename} to Parquet...")

                    try:
                        df = pd.read_csv(
                            csv_path,
                            usecols=[0, 1, 5],
                            names=["YMD", "Time", "Close"],
                            header=None,
                            skiprows=1
                        )
                        df.to_parquet(parquet_path, index=False)
                        print(f"Saved to {parquet_path}")
                    except Exception as e:
                        print(f"Failed to convert {filename}: {e}")

#base_folder = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting\Thursday_output_folder"
#parquet_base_folder = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting\Parquet_Files\Thursday_output_folder"
#convert_csvs_to_parquet(base_folder, parquet_base_folder)


def convert_single_csv_to_parquet():

    # CSV file path (relative or absolute)
    csv_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\INDIA VIX_minute.csv"

    # Load CSV
    df = pd.read_csv(csv_path)

    # Get directory and base filename (without extension)
    csv_dir = os.path.dirname(os.path.abspath(csv_path))
    csv_basename = os.path.splitext(os.path.basename(csv_path))[0]

    # Create new Parquet path
    parquet_path = os.path.join(csv_dir, f"{csv_basename}.parquet")

    # Save to Parquet
    df.to_parquet(parquet_path, engine="pyarrow")  # or engine="fastparquet"

    print(f"Convert to csv to parquet. Saved to {parquet_path}")

#convert_single_csv_to_parquet()    

parquet_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\INDIA VIX_minute.parquet"
df = pd.read_parquet(parquet_path)
print(df.tail(100))