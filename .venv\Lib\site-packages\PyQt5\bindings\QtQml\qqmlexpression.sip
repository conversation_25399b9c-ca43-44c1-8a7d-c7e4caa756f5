// qqmlexpression.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlExpression : QObject
{
%TypeHeaderCode
#include <qqmlexpression.h>
%End

public:
    QQmlExpression();
    QQmlExpression(QQmlContext *, QObject *, const QString &, QObject *parent /TransferThis/ = 0);
    QQmlExpression(const QQmlScriptString &, QQmlContext *context = 0, QObject *scope = 0, QObject *parent /TransferThis/ = 0);
    virtual ~QQmlExpression();
    QQmlEngine *engine() const;
    QQmlContext *context() const;
    QString expression() const;
    void setExpression(const QString &);
    bool notifyOnValueChanged() const;
    void setNotifyOnValueChanged(bool);
    QString sourceFile() const;
    int lineNumber() const;
    int columnNumber() const;
    void setSourceLocation(const QString &fileName, int line, int column = 0);
    QObject *scopeObject() const;
    bool hasError() const;
    void clearError();
    QQmlError error() const;
    QVariant evaluate(bool *valueIsUndefined = 0) /ReleaseGIL/;

signals:
    void valueChanged();
};
