// qcolorspace.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_14_0 -)

class QColorSpace
{
%TypeHeaderCode
#include <qcolorspace.h>
%End

public:
    enum NamedColorSpace
    {
        SRgb,
        SRgbLinear,
        AdobeRgb,
        DisplayP3,
        ProPhotoRgb,
    };

    enum class Primaries
    {
        Custom,
        SRgb,
        AdobeRgb,
        DciP3D65,
        ProPhotoRgb,
    };

    enum class TransferFunction
    {
        Custom,
        Linear,
        Gamma,
        SRgb,
        ProPhotoRgb,
    };

    QColorSpace();
    QColorSpace(QColorSpace::NamedColorSpace namedColorSpace);
    QColorSpace(QColorSpace::Primaries primaries, QColorSpace::TransferFunction fun, float gamma = 0.F);
    QColorSpace(QColorSpace::Primaries primaries, float gamma);
    QColorSpace(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint, QColorSpace::TransferFunction fun, float gamma = 0.F);
    QColorSpace(const QColorSpace &colorSpace);
    ~QColorSpace();
    void swap(QColorSpace &colorSpace /Constrained/);
    QColorSpace::Primaries primaries() const;
    QColorSpace::TransferFunction transferFunction() const;
    float gamma() const;
    void setTransferFunction(QColorSpace::TransferFunction transferFunction, float gamma = 0.F);
    QColorSpace withTransferFunction(QColorSpace::TransferFunction transferFunction, float gamma = 0.F) const;
    void setPrimaries(QColorSpace::Primaries primariesId);
    void setPrimaries(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint);
    bool isValid() const;
    static QColorSpace fromIccProfile(const QByteArray &iccProfile);
    QByteArray iccProfile() const;
    QColorTransform transformationToColorSpace(const QColorSpace &colorspace) const;
};

%End
%If (Qt_5_14_0 -)
bool operator==(const QColorSpace &colorSpace1, const QColorSpace &colorSpace2);
%End
%If (Qt_5_14_0 -)
bool operator!=(const QColorSpace &colorSpace1, const QColorSpace &colorSpace2);
%End
%If (Qt_5_14_0 -)
QDataStream &operator<<(QDataStream &, const QColorSpace & /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_14_0 -)
QDataStream &operator>>(QDataStream &, QColorSpace & /Constrained/) /ReleaseGIL/;
%End
