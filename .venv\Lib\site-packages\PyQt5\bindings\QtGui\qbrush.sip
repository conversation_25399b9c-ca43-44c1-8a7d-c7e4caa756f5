// qbrush.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBrush /TypeHintIn="Union[QBrush, QColor, QGradient]"/
{
%TypeHeaderCode
#include <qbrush.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// QColor or a QGradient to be used whenever a QBrush is expected.  Note that
// SIP must process QColor before QBrush so that the former's QVariant cast
// operator is applied before the latter's.

if (sipIsErr == NULL)
    return (sipCanConvertToType(sipPy, sipType_QBrush, SIP_NO_CONVERTORS) ||
            sipCanConvertToType(sipPy, sipType_QColor, 0) ||
            sipCanConvertToType(sipPy, sipType_QGradient, 0));

if (sipCanConvertToType(sipPy, sipType_QBrush, SIP_NO_CONVERTORS))
{
    *sipCppPtr = reinterpret_cast<QBrush *>(sipConvertToType(sipPy, sipType_QBrush, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

int state;

if (sipCanConvertToType(sipPy, sipType_QColor, 0))
{
    QColor *c = reinterpret_cast<QColor *>(sipConvertToType(sipPy, sipType_QColor, 0, 0, &state, sipIsErr));

    if (*sipIsErr)
    {
        sipReleaseType(c, sipType_QColor, state);
        return 0;
    }

    *sipCppPtr = new QBrush(*c);

    sipReleaseType(c, sipType_QColor, state);

    return sipGetState(sipTransferObj);
}

QGradient *g = reinterpret_cast<QGradient *>(sipConvertToType(sipPy, sipType_QGradient, 0, 0, &state, sipIsErr));

if (*sipIsErr)
{
    sipReleaseType(g, sipType_QGradient, state);
    return 0;
}

*sipCppPtr = new QBrush(*g);

sipReleaseType(g, sipType_QGradient, state);

return sipGetState(sipTransferObj);
%End

public:
    QBrush();
    QBrush(Qt::BrushStyle bs);
    QBrush(const QColor &color, Qt::BrushStyle style = Qt::SolidPattern);
    QBrush(const QColor &color, const QPixmap &pixmap);
    QBrush(const QPixmap &pixmap);
    QBrush(const QImage &image);
    QBrush(const QBrush &brush);
    QBrush(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QBrush>())
            sipCpp = new QBrush(a0->value<QBrush>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QBrush();
    void setStyle(Qt::BrushStyle);
    QPixmap texture() const;
    void setTexture(const QPixmap &pixmap);
    void setColor(const QColor &color);
    const QGradient *gradient() const;
    bool isOpaque() const;
    bool operator==(const QBrush &b) const;
    bool operator!=(const QBrush &b) const;
    void setColor(Qt::GlobalColor acolor);
    Qt::BrushStyle style() const;
    const QColor &color() const;
    void setTextureImage(const QImage &image);
    QImage textureImage() const;
    void setTransform(const QTransform &);
    QTransform transform() const;
    void swap(QBrush &other /Constrained/);
};

QDataStream &operator>>(QDataStream &, QBrush & /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, const QBrush & /Constrained/) /ReleaseGIL/;
typedef QVector<QPair<qreal, QColor>> QGradientStops;

class QGradient
{
%TypeHeaderCode
#include <qbrush.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QGradient::ConicalGradient:
        sipType = sipType_QConicalGradient;
        break;
    
    case QGradient::LinearGradient:
        sipType = sipType_QLinearGradient;
        break;
    
    case QGradient::RadialGradient:
        sipType = sipType_QRadialGradient;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    enum CoordinateMode
    {
        LogicalMode,
        StretchToDeviceMode,
        ObjectBoundingMode,
%If (Qt_5_12_0 -)
        ObjectMode,
%End
    };

    enum Type
    {
        LinearGradient,
        RadialGradient,
        ConicalGradient,
        NoGradient,
    };

    enum Spread
    {
        PadSpread,
        ReflectSpread,
        RepeatSpread,
    };

%If (Qt_5_12_0 -)

    enum Preset
    {
        WarmFlame,
        NightFade,
        SpringWarmth,
        JuicyPeach,
        YoungPassion,
        LadyLips,
        SunnyMorning,
        RainyAshville,
        FrozenDreams,
        WinterNeva,
        DustyGrass,
        TemptingAzure,
        HeavyRain,
        AmyCrisp,
        MeanFruit,
        DeepBlue,
        RipeMalinka,
        CloudyKnoxville,
        MalibuBeach,
        NewLife,
        TrueSunset,
        MorpheusDen,
        RareWind,
        NearMoon,
        WildApple,
        SaintPetersburg,
        PlumPlate,
        EverlastingSky,
        HappyFisher,
        Blessing,
        SharpeyeEagle,
        LadogaBottom,
        LemonGate,
        ItmeoBranding,
        ZeusMiracle,
        OldHat,
        StarWine,
        HappyAcid,
        AwesomePine,
        NewYork,
        ShyRainbow,
        MixedHopes,
        FlyHigh,
        StrongBliss,
        FreshMilk,
        SnowAgain,
        FebruaryInk,
        KindSteel,
        SoftGrass,
        GrownEarly,
        SharpBlues,
        ShadyWater,
        DirtyBeauty,
        GreatWhale,
        TeenNotebook,
        PoliteRumors,
        SweetPeriod,
        WideMatrix,
        SoftCherish,
        RedSalvation,
        BurningSpring,
        NightParty,
        SkyGlider,
        HeavenPeach,
        PurpleDivision,
        AquaSplash,
        SpikyNaga,
        LoveKiss,
        CleanMirror,
        PremiumDark,
        ColdEvening,
        CochitiLake,
        SummerGames,
        PassionateBed,
        MountainRock,
        DesertHump,
        JungleDay,
        PhoenixStart,
        OctoberSilence,
        FarawayRiver,
        AlchemistLab,
        OverSun,
        PremiumWhite,
        MarsParty,
        EternalConstance,
        JapanBlush,
        SmilingRain,
        CloudyApple,
        BigMango,
        HealthyWater,
        AmourAmour,
        RiskyConcrete,
        StrongStick,
        ViciousStance,
        PaloAlto,
        HappyMemories,
        MidnightBloom,
        Crystalline,
        PartyBliss,
        ConfidentCloud,
        LeCocktail,
        RiverCity,
        FrozenBerry,
        ChildCare,
        FlyingLemon,
        NewRetrowave,
        HiddenJaguar,
        AboveTheSky,
        Nega,
        DenseWater,
        Seashore,
        MarbleWall,
        CheerfulCaramel,
        NightSky,
        MagicLake,
        YoungGrass,
        ColorfulPeach,
        GentleCare,
        PlumBath,
        HappyUnicorn,
        AfricanField,
        SolidStone,
        OrangeJuice,
        GlassWater,
        NorthMiracle,
        FruitBlend,
        MillenniumPine,
        HighFlight,
        MoleHall,
        SpaceShift,
        ForestInei,
        RoyalGarden,
        RichMetal,
        JuicyCake,
        SmartIndigo,
        SandStrike,
        NorseBeauty,
        AquaGuidance,
        SunVeggie,
        SeaLord,
        BlackSea,
        GrassShampoo,
        LandingAircraft,
        WitchDance,
        SleeplessNight,
        AngelCare,
        CrystalRiver,
        SoftLipstick,
        SaltMountain,
        PerfectWhite,
        FreshOasis,
        StrictNovember,
        MorningSalad,
        DeepRelief,
        SeaStrike,
        NightCall,
        SupremeSky,
        LightBlue,
        MindCrawl,
        LilyMeadow,
        SugarLollipop,
        SweetDessert,
        MagicRay,
        TeenParty,
        FrozenHeat,
        GagarinView,
        FabledSunset,
        PerfectBlue,
%If (Qt_5_14_0 -)
        NumPresets,
%End
    };

%End
    QGradient();
%If (Qt_5_12_0 -)
    QGradient(QGradient::Preset);
%End
%If (Qt_5_14_0 -)
    ~QGradient();
%End
    QGradient::Type type() const;
    QGradient::Spread spread() const;
    void setColorAt(qreal pos, const QColor &color);
    void setStops(const QGradientStops &stops);
    QGradientStops stops() const;
    bool operator==(const QGradient &gradient) const;
    bool operator!=(const QGradient &other) const;
    void setSpread(QGradient::Spread aspread);
    QGradient::CoordinateMode coordinateMode() const;
    void setCoordinateMode(QGradient::CoordinateMode mode);
};

class QLinearGradient : QGradient
{
%TypeHeaderCode
#include <qbrush.h>
%End

public:
    QLinearGradient();
    QLinearGradient(const QPointF &start, const QPointF &finalStop);
    QLinearGradient(qreal xStart, qreal yStart, qreal xFinalStop, qreal yFinalStop);
%If (Qt_5_14_0 -)
    ~QLinearGradient();
%End
    QPointF start() const;
    QPointF finalStop() const;
    void setStart(const QPointF &start);
    void setStart(qreal x, qreal y);
    void setFinalStop(const QPointF &stop);
    void setFinalStop(qreal x, qreal y);
};

class QRadialGradient : QGradient
{
%TypeHeaderCode
#include <qbrush.h>
%End

public:
    QRadialGradient();
    QRadialGradient(const QPointF &center, qreal radius, const QPointF &focalPoint);
    QRadialGradient(const QPointF &center, qreal centerRadius, const QPointF &focalPoint, qreal focalRadius);
    QRadialGradient(const QPointF &center, qreal radius);
    QRadialGradient(qreal cx, qreal cy, qreal radius, qreal fx, qreal fy);
    QRadialGradient(qreal cx, qreal cy, qreal centerRadius, qreal fx, qreal fy, qreal focalRadius);
    QRadialGradient(qreal cx, qreal cy, qreal radius);
%If (Qt_5_14_0 -)
    ~QRadialGradient();
%End
    QPointF center() const;
    QPointF focalPoint() const;
    qreal radius() const;
    void setCenter(const QPointF &center);
    void setCenter(qreal x, qreal y);
    void setFocalPoint(const QPointF &focalPoint);
    void setFocalPoint(qreal x, qreal y);
    void setRadius(qreal radius);
    qreal centerRadius() const;
    void setCenterRadius(qreal radius);
    qreal focalRadius() const;
    void setFocalRadius(qreal radius);
};

class QConicalGradient : QGradient
{
%TypeHeaderCode
#include <qbrush.h>
%End

public:
    QConicalGradient();
    QConicalGradient(const QPointF &center, qreal startAngle);
    QConicalGradient(qreal cx, qreal cy, qreal startAngle);
%If (Qt_5_14_0 -)
    ~QConicalGradient();
%End
    QPointF center() const;
    qreal angle() const;
    void setCenter(const QPointF &center);
    void setCenter(qreal x, qreal y);
    void setAngle(qreal angle);
};
