// qtoolbutton.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QToolButton : QAbstractButton
{
%TypeHeaderCode
#include <qtoolbutton.h>
%End

public:
    enum ToolButtonPopupMode
    {
        DelayedPopup,
        MenuButtonPopup,
        InstantPopup,
    };

    explicit QToolButton(QWidget *parent /TransferThis/ = 0);
    virtual ~QToolButton();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    Qt::ToolButtonStyle toolButtonStyle() const;
    Qt::ArrowType arrowType() const;
    void setArrowType(Qt::ArrowType type);
    void setMenu(QMenu *menu /KeepReference/);
    QMenu *menu() const;
    void setPopupMode(QToolButton::ToolButtonPopupMode mode);
    QToolButton::ToolButtonPopupMode popupMode() const;
    QAction *defaultAction() const;
    void setAutoRaise(bool enable);
    bool autoRaise() const;

public slots:
    void showMenu();
    void setToolButtonStyle(Qt::ToolButtonStyle style);
    void setDefaultAction(QAction * /KeepReference/);

signals:
    void triggered(QAction *);

protected:
    void initStyleOption(QStyleOptionToolButton *option) const;
    virtual bool event(QEvent *e);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void actionEvent(QActionEvent *);
    virtual void enterEvent(QEvent *);
    virtual void leaveEvent(QEvent *);
    virtual void timerEvent(QTimerEvent *);
    virtual void changeEvent(QEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void nextCheckState();
    virtual bool hitButton(const QPoint &pos) const;
};
