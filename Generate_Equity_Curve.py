import pandas as pd
import matplotlib.pyplot as plt
import os

def generate_equity_curve(equity_log, output_dir="."):
    """
    Given a list of dicts with 'date' and 'pnl', generate and save equity curve.

    Parameters:
        equity_log (list): List of dicts like [{'date': '20240701', 'pnl': 1200}, ...]
        output_dir (str): Folder where CSV and PNG files are saved

    Returns:
        pd.DataFrame: DataFrame with columns [date, pnl, equity]
    """
    if not equity_log:
        print("No P&L data available to generate equity curve.")
        return None

    equity_df = pd.DataFrame(equity_log)
    equity_df['date'] = pd.to_datetime(equity_df['date'], format='%Y%m%d')
    equity_df = equity_df.sort_values('date')
    equity_df['equity'] = equity_df['pnl'].cumsum()

    # Save to CSV
    csv_path = os.path.join(output_dir, "equity_curve.csv")
    equity_df.to_csv(csv_path, index=False)

    # Plot and save equity curve
    plt.figure(figsize=(12, 6))
    plt.plot(equity_df['date'], equity_df['equity'], marker='o')
    plt.title('Equity Curve (Cumulative P&L)')
    plt.xlabel('Date')
    plt.ylabel('Equity')
    plt.grid(True)
    plt.tight_layout()
    plot_path = os.path.join(output_dir, "equity_curve.png")
    plt.savefig(plot_path)
    plt.close()

    print(f"Equity curve saved to {csv_path} and {plot_path}")
    return equity_df
