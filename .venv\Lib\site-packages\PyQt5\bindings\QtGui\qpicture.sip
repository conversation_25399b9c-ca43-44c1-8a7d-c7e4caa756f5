// qpicture.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPicture : QPaintDevice
{
%TypeHeaderCode
#include <qpicture.h>
%End

public:
    explicit QPicture(int formatVersion = -1);
    QPicture(const QPicture &);
    virtual ~QPicture();
    bool isNull() const;
    virtual int devType() const;
    uint size() const;
    const char *data() const /Encoding="None"/;
    virtual void setData(const char *data /Array/, uint size /ArraySize/);
    bool play(QPainter *p);
    bool load(QIODevice *dev, const char *format = 0) /ReleaseGIL/;
    bool load(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    bool save(QIODevice *dev, const char *format = 0) /ReleaseGIL/;
    bool save(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    QRect boundingRect() const;
    void setBoundingRect(const QRect &r);
    void detach();
    bool isDetached() const;
    virtual QPaintEngine *paintEngine() const;

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric m) const;

public:
    void swap(QPicture &other /Constrained/);
};

class QPictureIO
{
%TypeHeaderCode
#include <qpicture.h>
%End

%TypeCode
// This defines the mapping between picture formats and the corresponding
// Python i/o handler callables.
struct qtgui_pio {
    const char *format;     // The format.
    PyObject *read;         // The read handler.
    PyObject *write;        // The write handler.
    qtgui_pio *next;        // The next in the list.
};


// The head of the list.
static qtgui_pio *qtgui_pio_head = 0;


// Find the entry for the given picture.
static const qtgui_pio *qtgui_pio_find(QPictureIO *pio)
{
    for (const qtgui_pio *p = qtgui_pio_head; p; p = p->next)
        if (qstrcmp(pio->format(), p->format) == 0)
            return p;

    return 0;
}


// This is the C++ read handler.
static void qtgui_pio_read(QPictureIO *pio)
{
    const qtgui_pio *p = qtgui_pio_find(pio);

    if (p && p->read)
    {
        Py_XDECREF(sipCallMethod(0, p->read, "D", pio, sipType_QPictureIO, NULL));
    }
}


// This is the C++ write handler.
static void qtgui_pio_write(QPictureIO *pio)
{
    const qtgui_pio *p = qtgui_pio_find(pio);

    if (p && p->write)
    {
        Py_XDECREF(sipCallMethod(0, p->write, "D", pio, sipType_QPictureIO, NULL));
    }
}
%End

public:
    QPictureIO();
    QPictureIO(QIODevice *ioDevice, const char *format);
    QPictureIO(const QString &fileName, const char *format);
    ~QPictureIO();
    const QPicture &picture() const;
    int status() const;
    const char *format() const;
    QIODevice *ioDevice() const;
    QString fileName() const;
    int quality() const;
    QString description() const;
    const char *parameters() const;
    float gamma() const;
    void setPicture(const QPicture &);
    void setStatus(int);
    void setFormat(const char *);
    void setIODevice(QIODevice *);
    void setFileName(const QString &);
    void setQuality(int);
    void setDescription(const QString &);
    void setParameters(const char *);
    void setGamma(float);
    bool read() /ReleaseGIL/;
    bool write() /ReleaseGIL/;
    static QByteArray pictureFormat(const QString &fileName);
    static QByteArray pictureFormat(QIODevice *);
    static QList<QByteArray> inputFormats();
    static QList<QByteArray> outputFormats();
    static void defineIOHandler(const char *format, const char *header, const char *flags, SIP_PYCALLABLE read_picture /AllowNone,TypeHint="Optional[Callable[[QPictureIO], None]]"/, SIP_PYCALLABLE write_picture /AllowNone,TypeHint="Optional[Callable[[QPictureIO], None]]"/);
%MethodCode
        // Convert None to NULL.
        if (a3 == Py_None)
            a3 = 0;
        
        if (a4 == Py_None)
            a4 = 0;
        
        // See if we already know about the format.
        qtgui_pio *p;
        
        for (p = qtgui_pio_head; p; p = p->next)
            if (qstrcmp(a0, p->format) == 0)
                break;
        
        if (!p)
        {
            // Handle the new format.
            p = new qtgui_pio;
            p->format = qstrdup(a0);
            p->read = 0;
            p->write = 0;
            p->next = qtgui_pio_head;
        
            qtgui_pio_head = p;
        }
        
        // Replace the old callables with the new ones.
        Py_XDECREF(p->read);
        p->read = a3;
        Py_XINCREF(p->read);
        
        Py_XDECREF(p->write);
        p->write = a4;
        Py_XINCREF(p->write);
        
        // Install the generic handlers.
        QPictureIO::defineIOHandler(a0, a1, a2, qtgui_pio_read, qtgui_pio_write);
%End

private:
    QPictureIO(const QPictureIO &);
};

QDataStream &operator<<(QDataStream &in, const QPicture &p /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QPicture &p /Constrained/) /ReleaseGIL/;
