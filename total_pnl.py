import os
import logger_config

logger = logger_config.get_logger('main_logger')

def calculate_total_pnl_per_minute(positions, ce_sell_close, pe_sell_close,ce_buy_close, pe_buy_close):
    """
    Calculate the total P&L for the current positions at a given minute.
    
    :param positions: Dictionary of current positions
    :param ce_price: Current CE price
    :param pe_price: Current PE price
    :return: Total P&L for the current minute
    """
    total_pnl = 0  # Initialize total P&L
    qty = int(os.getenv("GLOBAL_QTY"))

    for key, pos in positions.items():
        if not pos.get('closed', False):
            if pos['side'] == 'SELL':
                if 'CE' in key:
                    current_price = ce_sell_close
                elif 'PE' in key:
                    current_price = pe_sell_close
            else:
                if 'CE' in key:
                    current_price = ce_buy_close
                elif 'PE' in key:
                    current_price = pe_buy_close

            # Calculate P&L
            entry_price = pos.get('ltp')
            side = pos.get('side', 'SELL')  # Default to SELL if not provided
           
            
            if entry_price is not None and current_price is not None:
                if side == 'SELL':
                    pos['pnl'] = (entry_price - current_price) * qty
                    logger.info(f"(entry_price {entry_price} - current_price {current_price}) * qty {qty}")
                else:  # BUY
                    pos['pnl'] = (current_price - entry_price) * qty
                    logger.info(f"(current_price {current_price} - entry_price {entry_price}) * qty {qty}")

                total_pnl += pos['pnl']  # Accumulate P&L
                logger.info(f"{key}: {side} | Entry: {entry_price}, LTP: {current_price}, Qty: {qty}, PnL: {pos['pnl']}")

            else:
                pos['pnl'] = None  # In case price data is missing

    return total_pnl
