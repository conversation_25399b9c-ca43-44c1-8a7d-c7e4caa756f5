// qgeocodereply.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QGeoCodeReply : QObject
{
%TypeHeaderCode
#include <qgeocodereply.h>
%End

public:
    enum <PERSON>r
    {
        NoE<PERSON>r,
        EngineNotSetError,
        CommunicationError,
        ParseError,
        UnsupportedOptionError,
        CombinationError,
        UnknownError,
    };

    QGeoCodeReply(QGeoCodeReply::Error error, const QString &errorString, QObject *parent /TransferThis/ = 0);
    virtual ~QGeoCodeReply();
    bool isFinished() const;
    QGeoCodeReply::Error error() const;
    QString errorString() const;
    QGeoShape viewport() const;
    QList<QGeoLocation> locations() const;
    int limit() const;
    int offset() const;
    virtual void abort();

signals:
%If (Qt_5_9_0 -)
    void aborted();
%End
    void finished();
    void error(QGeoCodeReply::Error error, const QString &errorString = QString());

protected:
%If (Qt_5_6_1 -)
    explicit QGeoCodeReply(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QGeoCodeReply(QObject *parent /TransferThis/ = 0);
%End
    void setError(QGeoCodeReply::Error error, const QString &errorString);
    void setFinished(bool finished);
    void setViewport(const QGeoShape &viewport);
    void addLocation(const QGeoLocation &location);
    void setLocations(const QList<QGeoLocation> &locations);
    void setLimit(int limit);
    void setOffset(int offset);
};

%End
