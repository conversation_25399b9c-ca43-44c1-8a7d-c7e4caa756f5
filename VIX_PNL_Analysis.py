import pandas as pd
import matplotlib.pyplot as plt

# Load your Excel file
df = pd.read_excel(r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\EC\equity_curve_vix.xlsx")

# Step 1: Create VIX Buckets with more granularity
bins = [0, 16,17, 18, 20, 22, 24, 26, 28, df['vix_close_value'].max()]
labels = ['<16','16-17', '16-18', '18-20', '20-22', '22-24', '24-26', '26-28', '>28']
df['vix_bucket'] = pd.cut(df['vix_close_value'], bins=bins, labels=labels)

# Step 2: Add is_loss and is_stop_loss columns
df['is_loss'] = df['pnl'] < 0
df['is_stop_loss'] = df['exit_reason'] == 'STOP_LOSS'  # Assuming STOP_LOSS is indicated in the exit_reason column

# Step 3: Group and Analyze
summary = df.groupby('vix_bucket').agg(
    avg_pnl=('pnl', 'mean'),
    median_pnl=('pnl', 'median'),
    trade_count=('pnl', 'count'),
    loss_count=('is_loss', 'sum'),
    stop_loss_count=('is_stop_loss', 'sum')
).reset_index()

summary['loss_pct'] = (summary['loss_count'] / summary['trade_count']) * 100
summary['stop_loss_pct'] = (summary['stop_loss_count'] / summary['trade_count']) * 100

# Display the summary
print(summary)

# Optional: Plot
fig, ax1 = plt.subplots(figsize=(10, 6))

# Bar chart for Avg PnL
summary.plot(kind='bar', x='vix_bucket', y='avg_pnl', ax=ax1, color='skyblue', position=0, width=0.4, legend=False)
ax1.set_ylabel("Average PnL", color='blue')
ax1.set_xlabel("VIX Bucket")
ax1.set_title("VIX Buckets vs Average PnL, % Losses, and % STOP_LOSS")

# Line chart for % Loss and % STOP_LOSS
ax2 = ax1.twinx()
ax2.plot(summary['vix_bucket'], summary['loss_pct'], color='red', marker='o', label='% Loss')
ax2.plot(summary['vix_bucket'], summary['stop_loss_pct'], color='green', marker='x', label='% STOP_LOSS')
ax2.set_ylabel("% Trades", color='red')

plt.tight_layout()
plt.show()