// qnetworkdiskcache.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkDiskCache : QAbstractNetworkCache
{
%TypeHeaderCode
#include <qnetworkdiskcache.h>
%End

public:
    explicit QNetworkDiskCache(QObject *parent /TransferThis/ = 0);
    virtual ~QNetworkDiskCache();
    QString cacheDirectory() const;
    void setCacheDirectory(const QString &cacheDir);
    qint64 maximumCacheSize() const;
    void setMaximumCacheSize(qint64 size);
    virtual qint64 cacheSize() const;
    virtual QNetworkCacheMetaData metaData(const QUrl &url);
    virtual void updateMetaData(const QNetworkCacheMetaData &metaData);
    virtual QIODevice *data(const QUrl &url) /Factory/;
    virtual bool remove(const QUrl &url);
    virtual QIODevice *prepare(const QNetworkCacheMetaData &metaData);
    virtual void insert(QIODevice *device);
    QNetworkCacheMetaData fileMetaData(const QString &fileName) const;

public slots:
    virtual void clear();

protected:
    virtual qint64 expire();
};
