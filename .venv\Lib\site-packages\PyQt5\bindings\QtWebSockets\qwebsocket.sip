// qwebsocket.sip generated by MetaSIP
//
// This file is part of the QtWebSockets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_3_0 -)

class QWebSocket : QObject
{
%TypeHeaderCode
#include <qwebsocket.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QWebSocket, &sipType_QWebSocket, -1, 1},
        {sipName_QWebSocketServer, &sipType_QWebSocketServer, -1, 2},
        {sipName_QMaskGenerator, &sipType_QMaskGenerator, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QWebSocket(const QString &origin = QString(), QWebSocketProtocol::Version version = QWebSocketProtocol::VersionLatest, QObject *parent /TransferThis/ = 0);
    virtual ~QWebSocket();
    void abort();
    QAbstractSocket::SocketError error() const;
    QString errorString() const;
    bool flush() /ReleaseGIL/;
    bool isValid() const;
    QHostAddress localAddress() const;
    quint16 localPort() const;
    QAbstractSocket::PauseModes pauseMode() const;
    QHostAddress peerAddress() const;
    QString peerName() const;
    quint16 peerPort() const;
    QNetworkProxy proxy() const;
    void setProxy(const QNetworkProxy &networkProxy);
    void setMaskGenerator(const QMaskGenerator *maskGenerator /KeepReference/);
    const QMaskGenerator *maskGenerator() const;
    qint64 readBufferSize() const;
    void setReadBufferSize(qint64 size);
    void resume() /ReleaseGIL/;
    void setPauseMode(QAbstractSocket::PauseModes pauseMode);
    QAbstractSocket::SocketState state() const;
    QWebSocketProtocol::Version version() const;
    QString resourceName() const;
    QUrl requestUrl() const;
    QString origin() const;
    QWebSocketProtocol::CloseCode closeCode() const;
    QString closeReason() const;
    qint64 sendTextMessage(const QString &message) /ReleaseGIL/;
    qint64 sendBinaryMessage(const QByteArray &data) /ReleaseGIL/;
%If (PyQt_SSL)
    void ignoreSslErrors(const QList<QSslError> &errors);
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &sslConfiguration);
%End
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (Qt_5_6_0 -)
    QNetworkRequest request() const;
%End

public slots:
    void close(QWebSocketProtocol::CloseCode closeCode = QWebSocketProtocol::CloseCodeNormal, const QString &reason = QString()) /ReleaseGIL/;
    void open(const QUrl &url) /ReleaseGIL/;
%If (Qt_5_6_0 -)
    void open(const QNetworkRequest &request) /ReleaseGIL/;
%End
    void ping(const QByteArray &payload = QByteArray()) /ReleaseGIL/;
%If (PyQt_SSL)
    void ignoreSslErrors();
%End

signals:
    void aboutToClose();
    void connected();
    void disconnected();
    void stateChanged(QAbstractSocket::SocketState state);
    void proxyAuthenticationRequired(const QNetworkProxy &proxy, QAuthenticator *pAuthenticator);
    void readChannelFinished();
    void textFrameReceived(const QString &frame, bool isLastFrame);
    void binaryFrameReceived(const QByteArray &frame, bool isLastFrame);
    void textMessageReceived(const QString &message);
    void binaryMessageReceived(const QByteArray &message);
    void error(QAbstractSocket::SocketError error);
    void pong(quint64 elapsedTime, const QByteArray &payload);
    void bytesWritten(qint64 bytes);
%If (PyQt_SSL)
    void sslErrors(const QList<QSslError> &errors);
%End
%If (Qt_5_8_0 -)
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End
%End

public:
%If (Qt_5_12_0 -)
    qint64 bytesToWrite() const;
%End
%If (Qt_5_15_0 -)
    void setMaxAllowedIncomingFrameSize(quint64 maxAllowedIncomingFrameSize);
%End
%If (Qt_5_15_0 -)
    quint64 maxAllowedIncomingFrameSize() const;
%End
%If (Qt_5_15_0 -)
    void setMaxAllowedIncomingMessageSize(quint64 maxAllowedIncomingMessageSize);
%End
%If (Qt_5_15_0 -)
    quint64 maxAllowedIncomingMessageSize() const;
%End
%If (Qt_5_15_0 -)
    static quint64 maxIncomingMessageSize();
%End
%If (Qt_5_15_0 -)
    static quint64 maxIncomingFrameSize();
%End
%If (Qt_5_15_0 -)
    void setOutgoingFrameSize(quint64 outgoingFrameSize);
%End
%If (Qt_5_15_0 -)
    quint64 outgoingFrameSize() const;
%End
%If (Qt_5_15_0 -)
    static quint64 maxOutgoingFrameSize();
%End
};

%End
