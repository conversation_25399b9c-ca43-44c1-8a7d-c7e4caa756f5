import os
import re
import time
from datetime import datetime, timedelta
from BT_Thursday_New_Recreate_Strategy import thursday_execute_BCS
from Monitor_Prices import monitor_prices
import logger_config
import random
from datetime import date
#from Generate_Equity_Curve import generate_equity_curve
import csv
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
from load_parquet_in_memory import load_parquet_data, list_date_folders


logger = logger_config.setup_logger('main_logger', 'main.log')

''''
def format_execution_time(seconds):
    """Convert seconds to hh:mm:ss:ms format - Ultra fast version."""
    # Convert to milliseconds using bit operations for speed
    total_ms = int(seconds * 1000)
    ms = total_ms % 1000
    total_sec = total_ms // 1000
    sec = total_sec % 60
    total_min = total_sec // 60
    min = total_min % 60
    hr = total_min // 60
    # Use % formatting which is faster than f-strings for simple cases
    return "%02d:%02d:%02d:%03d" % (hr, min, sec, ms)

'''
def get_randomized_times(target_date):
    """Calculate randomized start and exit times based on configuration."""

    # Seed random with the provided trading date for consistency throughout the day
    random.seed(target_date.isoformat())

    # Get base times from environment
    start_base = datetime.strptime(os.getenv('START_BASE_TIME'), '%H:%M').time()
    exit_base = datetime.strptime(os.getenv('EXIT_BASE_TIME'), '%H:%M').time()

    # Get variation ranges
    start_variation = int(os.getenv('START_TIME_VARIATION', '10'))
    exit_variation = int(os.getenv('EXIT_TIME_VARIATION', '10'))

    # Calculate random offsets
    start_offset = random.randint(-start_variation, start_variation)
    exit_offset = random.randint(-exit_variation, exit_variation)

    # Apply offsets
    start_dt = datetime.combine(date.today(), start_base)
    exit_dt = datetime.combine(date.today(), exit_base)

    start_dt = start_dt + timedelta(minutes=start_offset)
    exit_dt = exit_dt + timedelta(minutes=exit_offset)

    return start_dt.time(), exit_dt.time()


def run_for_one_day(mainkey, subfolders,start_time, exit_time,stop_loss, target_profit, ratio_check, resize_factor):
    """
    Run the existing back‑test pipeline for a single YYYYMMDD folder.
    This is your original code, moved unchanged into its own function.
    """
    start_time_process = time.time()
    logger.info("Starting the main process...")

    # --- Identify the date from the folder name --- #
    folder_date = os.path.basename(mainkey)
    folder_date_dt = datetime.strptime(folder_date, "%Y%m%d")
    logger.info(f"Folder Date: {folder_date_dt.strftime('%Y-%m-%d')}")

    # --- Subfolders and their sides (unchanged) --- #
    sides = {
        "CE_SELL": "SELL",
        "PE_SELL": "SELL",
        "CE_BUY":  "BUY",
        "PE_BUY":  "BUY"
    }

    # --- Get randomized start/exit times --- #
    start_execution_time = time.time()
    #start_time, exit_time = get_randomized_times(folder_date_dt)

    # --- Use hardcoded start/exit times --- #
    start_time = datetime.strptime("09:45:00", "%H:%M:%S").time()
    exit_time = datetime.strptime("15:10:00", "%H:%M:%S").time()

    
    logger.info(f"Backtesting trading window: Start at {start_time}, Exit at {exit_time}")
    #logger.info(f"get_randomized_times() execution time: {format_execution_time(time.time()-start_execution_time)}")
    logger.info(f"get_randomized_times() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Strategy + monitoring (unchanged) --- #
    start_execution_time = time.time()
    BCS_positions, vix_close_value = thursday_execute_BCS(mainkey, subfolders, start_time, exit_time, folder_date_dt,stop_loss, target_profit, ratio_check, resize_factor)
    
    if BCS_positions is None:
        logger.info(f"Skipping folder {mainkey} due to missing VIX data or other issues.")
        return None, None
    logger.info(f"BCS_positions: {BCS_positions}")
    #logger.info(f"thursday_execute_BCS() execution time: {format_execution_time(time.time()-start_execution_time)}")
    logger.info(f"thursday_execute_BCS() execution time: {time.time()-start_execution_time:.6f}s")

    start_execution_time = time.time()
    total_pnl, exit_reason  =monitor_prices(BCS_positions, mainkey, start_time, exit_time, folder_date, target_profit, stop_loss,ratio_check)
    #logger.info(f"monitor_prices() execution time: {format_execution_time(time.time()-start_execution_time)}")
    logger.info(f"monitor_prices() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Finish up --- #
    processing_time = time.time() - start_time_process
    #logger.info(f"Processing time: {format_execution_time(processing_time)}")
    logger.info(f"Processing time: {str(timedelta(seconds=int(processing_time)))}")

    logger.info("End of day ----------------------------------------------\n")
    
    return folder_date,total_pnl, exit_reason, vix_close_value

# ------------- 2. NEW driver that loops through all date folders ------------- #
def main():
    start_time_main = time.time()
    
    root_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting_DataFrames_V2\Parquet_Files\Thursday_output_folder"
    
    # Retrieve start and end dates from environment variables
    start_date_str = os.getenv("START_DATE")
    logger.info(f"START_DATE: {start_date_str}")
    end_date_str = os.getenv("END_DATE")
    logger.info(f"END_DATE: {end_date_str}")

    try:
        from_date = datetime.strptime(start_date_str, "%Y%m%d")
        to_date = datetime.strptime(end_date_str, "%Y%m%d")
    except ValueError as e:
        logger.error(f"Invalid date format in environment variables: {e}")
        return
    
    start_time_list_date_folders = time.time()
    all_folders = list_date_folders(root_path, from_date, to_date)
    #logger.info(f"list_date_folders() execution time: {format_execution_time(time.time()-start_time_list_date_folders)}")
    logger.info(f"list_date_folders() execution time: {time.time()-start_time_list_date_folders:.6f}s")

    # Load all Parquet data into memory
    start_time_load_parquet_data = time.time()
    data = load_parquet_data(all_folders)
    #logger.info(f"load_parquet_data() execution time: {format_execution_time(time.time()-start_time_load_parquet_data)}")
    logger.info(f"load_parquet_data() execution time: {time.time()-start_time_load_parquet_data:.6f}s")


    '''
    #Start printing data dict for debugging purpose
    
    # Print the top-level keys of the data dictionary
    print("Top-level keys in data dictionary:", data.keys())

    # Iterate over the top-level keys and print the sub-keys and a sample of the data
    for folder, subfolders in data.items():
        print(f"\nFolder: {folder}")
        for subfolder, file_dict in subfolders.items():
            print(f"  Subfolder: {subfolder}")
            for filename, df in file_dict.items():
                print(f"    File: {filename}")
                print(df.head())

    #End printing data dict for debugging purpose
    '''

    if not data:
        logger.warning("No dataframes loaded. Please check the directory and file extensions.")
        return
    
    exit_reason_counts = {
    "STOP_LOSS": 0,
    "TARGET_PROFIT": 0,
    "RATIO_HIT": 0,
    "TIME_EXIT": 0
    }
    
    equity_log = []  # Store date-wise total_pnl here

    # ---- Run the original logic for each day ---- #
    for mainkey, subfolders in data.items():
        print(f"Processing folder: {mainkey}")
        #print(f"Subkey: {subkey}")
        folder_date = None  # Initialize folder_date before the try block
        try:
            # Pass the entire subfolder data to run_for_one_day
            pnl, exit_reason, vix_close_value = run_for_one_day(mainkey, subfolders)
            folder_date = mainkey.split('\\')[-1]
            equity_log.append({'date': folder_date, 'pnl': pnl, 'exit_reason': exit_reason, 'vix_close_value': vix_close_value})
            
            if exit_reason is not None:
                exit_reason_counts[exit_reason] += 1
            else:
                logger.warning(f"No exit reason for folder {folder_date}. Skipping count update.")

        except Exception as e:
            logger.exception(f"Error while processing folder {folder_date}: {e}")

    # Generate equity curve
    #generate_equity_curve(equity_log, output_dir=".")

    processing_time = time.time() - start_time_main
    #logger.info(f"Total execution time: {format_execution_time(processing_time)}")
    logger.info(f"Total execution time: {str(timedelta(seconds=int(processing_time)))}")


    logger.info("----- Exit Reason Summary -----")
    for reason, count in exit_reason_counts.items():
        logger.info(f"{reason}: {count}")


if __name__ == "__main__":
    main()
