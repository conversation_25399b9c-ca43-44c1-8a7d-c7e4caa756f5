// qgeoareamonitorinfo.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoAreaMonitorInfo
{
%TypeHeaderCode
#include <qgeoareamonitorinfo.h>
%End

public:
    explicit QGeoAreaMonitorInfo(const QString &name = QString());
    QGeoAreaMonitorInfo(const QGeoAreaMonitorInfo &other);
    ~QGeoAreaMonitorInfo();
    bool operator==(const QGeoAreaMonitorInfo &other) const;
    bool operator!=(const QGeoAreaMonitorInfo &other) const;
    QString name() const;
    void setName(const QString &name);
    QString identifier() const;
    bool isValid() const;
    QGeoShape area() const;
    void setArea(const QGeoShape &newShape);
    QDateTime expiration() const;
    void setExpiration(const QDateTime &expiry);
    bool isPersistent() const;
    void setPersistent(bool isPersistent);
    QVariantMap notificationParameters() const;
    void setNotificationParameters(const QVariantMap &parameters);
};

%End
%If (Qt_5_2_0 -)
QDataStream &operator<<(QDataStream &, const QGeoAreaMonitorInfo & /Constrained/);
%End
%If (Qt_5_2_0 -)
QDataStream &operator>>(QDataStream &, QGeoAreaMonitorInfo & /Constrained/);
%End
