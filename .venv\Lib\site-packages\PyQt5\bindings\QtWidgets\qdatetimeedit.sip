// qdatetimeedit.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDateTimeEdit : QAbstractSpinBox
{
%TypeHeaderCode
#include <qdatetimeedit.h>
%End

public:
    enum Section
    {
        NoSection,
        AmPmSection,
        MSecSection,
        SecondSection,
        MinuteSection,
        HourSection,
        DaySection,
        MonthSection,
        YearSection,
        TimeSections_Mask,
        DateSections_Mask,
    };

    typedef QFlags<QDateTimeEdit::Section> Sections;
    explicit QDateTimeEdit(QWidget *parent /TransferThis/ = 0);
    QDateTimeEdit(const QDateTime &datetime, QWidget *parent /TransferThis/ = 0);
    QDateTimeEdit(const QDate &date, QWidget *parent /TransferThis/ = 0);
    QDateTimeEdit(const QTime &time, QWidget *parent /TransferThis/ = 0);
    virtual ~QDateTimeEdit();
    QDateTime dateTime() const;
    QDate date() const;
    QTime time() const;
    QDate minimumDate() const;
    void setMinimumDate(const QDate &min);
    void clearMinimumDate();
    QDate maximumDate() const;
    void setMaximumDate(const QDate &max);
    void clearMaximumDate();
    void setDateRange(const QDate &min, const QDate &max);
    QTime minimumTime() const;
    void setMinimumTime(const QTime &min);
    void clearMinimumTime();
    QTime maximumTime() const;
    void setMaximumTime(const QTime &max);
    void clearMaximumTime();
    void setTimeRange(const QTime &min, const QTime &max);
    QDateTimeEdit::Sections displayedSections() const;
    QDateTimeEdit::Section currentSection() const;
    void setCurrentSection(QDateTimeEdit::Section section);
    QString sectionText(QDateTimeEdit::Section s) const;
    QString displayFormat() const;
    void setDisplayFormat(const QString &format);
    bool calendarPopup() const;
    void setCalendarPopup(bool enable);
    void setSelectedSection(QDateTimeEdit::Section section);
    virtual QSize sizeHint() const;
    virtual void clear();
    virtual void stepBy(int steps);
    virtual bool event(QEvent *e);
    QDateTimeEdit::Section sectionAt(int index) const;
    int currentSectionIndex() const;
    void setCurrentSectionIndex(int index);
    int sectionCount() const;

signals:
    void dateTimeChanged(const QDateTime &date);
    void timeChanged(const QTime &date);
    void dateChanged(const QDate &date);

public slots:
    void setDateTime(const QDateTime &dateTime);
    void setDate(const QDate &date);
    void setTime(const QTime &time);

protected:
    void initStyleOption(QStyleOptionSpinBox *option) const;
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual bool focusNextPrevChild(bool next);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    virtual void fixup(QString &input /In,Out/) const;
    virtual QDateTime dateTimeFromText(const QString &text) const;
    virtual QString textFromDateTime(const QDateTime &dt) const;
    virtual QAbstractSpinBox::StepEnabled stepEnabled() const;

public:
    QDateTime minimumDateTime() const;
    void clearMinimumDateTime();
    void setMinimumDateTime(const QDateTime &dt);
    QDateTime maximumDateTime() const;
    void clearMaximumDateTime();
    void setMaximumDateTime(const QDateTime &dt);
    void setDateTimeRange(const QDateTime &min, const QDateTime &max);
    QCalendarWidget *calendarWidget() const;
    void setCalendarWidget(QCalendarWidget *calendarWidget /Transfer/);
    Qt::TimeSpec timeSpec() const;
    void setTimeSpec(Qt::TimeSpec spec);
%If (Qt_5_14_0 -)
    QCalendar calendar() const;
%End
%If (Qt_5_14_0 -)
    void setCalendar(QCalendar calendar);
%End
};

class QTimeEdit : QDateTimeEdit
{
%TypeHeaderCode
#include <qdatetimeedit.h>
%End

public:
    explicit QTimeEdit(QWidget *parent /TransferThis/ = 0);
    QTimeEdit(const QTime &time, QWidget *parent /TransferThis/ = 0);
    virtual ~QTimeEdit();
};

class QDateEdit : QDateTimeEdit
{
%TypeHeaderCode
#include <qdatetimeedit.h>
%End

public:
    explicit QDateEdit(QWidget *parent /TransferThis/ = 0);
    QDateEdit(const QDate &date, QWidget *parent /TransferThis/ = 0);
    virtual ~QDateEdit();
};

QFlags<QDateTimeEdit::Section> operator|(QDateTimeEdit::Section f1, QFlags<QDateTimeEdit::Section> f2);
