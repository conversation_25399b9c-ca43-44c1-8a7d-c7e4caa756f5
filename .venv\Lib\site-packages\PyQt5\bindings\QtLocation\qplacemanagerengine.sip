// qplacemanagerengine.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QPlaceManagerEngine : QObject
{
%TypeHeaderCode
#include <qplacemanagerengine.h>
%End

public:
    QPlaceManagerEngine(const QVariantMap &parameters, QObject *parent /TransferThis/ = 0);
    virtual ~QPlaceManagerEngine();
    QString managerName() const;
    int managerVersion() const;
    virtual QPlaceDetailsReply *getPlaceDetails(const QString &placeId);
    virtual QPlaceContentReply *getPlaceContent(const QPlaceContentRequest &request);
    virtual QPlaceSearchReply *search(const QPlaceSearchRequest &request);
    virtual QPlaceSearchSuggestionReply *searchSuggestions(const QPlaceSearchRequest &request);
    virtual QPlaceIdReply *savePlace(const QPlace &place);
    virtual QPlaceIdReply *removePlace(const QString &placeId);
    virtual QPlaceIdReply *saveCategory(const QPlaceCategory &category, const QString &parentId);
    virtual QPlaceIdReply *removeCategory(const QString &categoryId);
    virtual QPlaceReply *initializeCategories();
    virtual QString parentCategoryId(const QString &categoryId) const;
    virtual QStringList childCategoryIds(const QString &categoryId) const;
    virtual QPlaceCategory category(const QString &categoryId) const;
    virtual QList<QPlaceCategory> childCategories(const QString &parentId) const;
    virtual QList<QLocale> locales() const;
    virtual void setLocales(const QList<QLocale> &locales);
    virtual QUrl constructIconUrl(const QPlaceIcon &icon, const QSize &size) const;
    virtual QPlace compatiblePlace(const QPlace &original) const;
    virtual QPlaceMatchReply *matchingPlaces(const QPlaceMatchRequest &request);

signals:
    void finished(QPlaceReply *reply);
    void error(QPlaceReply *, QPlaceReply::Error error, const QString &errorString = QString());
    void placeAdded(const QString &placeId);
    void placeUpdated(const QString &placeId);
    void placeRemoved(const QString &placeId);
    void categoryAdded(const QPlaceCategory &category, const QString &parentCategoryId);
    void categoryUpdated(const QPlaceCategory &category, const QString &parentCategoryId);
    void categoryRemoved(const QString &categoryId, const QString &parentCategoryId);
    void dataChanged();

protected:
    QPlaceManager *manager() const;
};

%End
