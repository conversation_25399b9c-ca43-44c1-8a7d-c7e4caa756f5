// qmessagebox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMessageBox : QDialog
{
%TypeHeaderCode
#include <qmessagebox.h>
%End

public:
    enum ButtonRole
    {
        InvalidRole,
        AcceptRole,
        <PERSON>jectR<PERSON>,
        DestructiveRole,
        ActionRole,
        HelpRole,
        YesRole,
        <PERSON>Role,
        ResetRole,
        ApplyRole,
    };

    enum Icon
    {
        NoIcon,
        Information,
        Warning,
        Critical,
        Question,
    };

    enum StandardButton
    {
        NoButton,
        Ok,
        Save,
        SaveAll,
        Open,
        Yes,
        YesToAll,
        No,
        NoToAll,
        Abort,
        Retry,
        Ignore,
        Close,
        Cancel,
        Discard,
        Help,
        Apply,
        Reset,
        RestoreDefaults,
        FirstButton,
        LastButton,
        YesAll,
        NoAll,
        Default,
        Escape,
        FlagMask,
        ButtonMask,
    };

    typedef QFlags<QMessageBox::StandardButton> StandardButtons;
    typedef QMessageBox::StandardButton Button;
    explicit QMessageBox(QWidget *parent /TransferThis/ = 0);
    QMessageBox(QMessageBox::Icon icon, const QString &title, const QString &text, QMessageBox::StandardButtons buttons = QMessageBox::NoButton, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::Dialog | Qt::MSWindowsFixedSizeDialogHint);
    virtual ~QMessageBox();
    QString text() const;
    void setText(const QString &);
    QMessageBox::Icon icon() const;
    void setIcon(QMessageBox::Icon);
    QPixmap iconPixmap() const;
    void setIconPixmap(const QPixmap &);
    Qt::TextFormat textFormat() const;
    void setTextFormat(Qt::TextFormat);
    static QMessageBox::StandardButton information(QWidget *parent, const QString &title, const QString &text, QMessageBox::StandardButtons buttons = QMessageBox::Ok, QMessageBox::StandardButton defaultButton = QMessageBox::NoButton) /ReleaseGIL/;
    static QMessageBox::StandardButton question(QWidget *parent, const QString &title, const QString &text, QMessageBox::StandardButtons buttons = QMessageBox::StandardButtons(QMessageBox::Yes | QMessageBox::No), QMessageBox::StandardButton defaultButton = QMessageBox::NoButton) /ReleaseGIL/;
    static QMessageBox::StandardButton warning(QWidget *parent, const QString &title, const QString &text, QMessageBox::StandardButtons buttons = QMessageBox::Ok, QMessageBox::StandardButton defaultButton = QMessageBox::NoButton) /ReleaseGIL/;
    static QMessageBox::StandardButton critical(QWidget *parent, const QString &title, const QString &text, QMessageBox::StandardButtons buttons = QMessageBox::Ok, QMessageBox::StandardButton defaultButton = QMessageBox::NoButton) /ReleaseGIL/;
    static void about(QWidget *parent, const QString &caption, const QString &text) /ReleaseGIL/;
    static void aboutQt(QWidget *parent, const QString &title = QString()) /ReleaseGIL/;
    static QPixmap standardIcon(QMessageBox::Icon icon);

protected:
    virtual bool event(QEvent *e);
    virtual void resizeEvent(QResizeEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void closeEvent(QCloseEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void changeEvent(QEvent *);

public:
    void addButton(QAbstractButton *button /Transfer/, QMessageBox::ButtonRole role);
    QPushButton *addButton(const QString &text, QMessageBox::ButtonRole role) /Transfer/;
    QPushButton *addButton(QMessageBox::StandardButton button) /Transfer/;
    void removeButton(QAbstractButton *button /TransferBack/);
    void setStandardButtons(QMessageBox::StandardButtons buttons);
    QMessageBox::StandardButtons standardButtons() const;
    QMessageBox::StandardButton standardButton(QAbstractButton *button) const;
    QAbstractButton *button(QMessageBox::StandardButton which) const;
    QPushButton *defaultButton() const;
    void setDefaultButton(QPushButton *button /KeepReference/);
    void setDefaultButton(QMessageBox::StandardButton button);
    QAbstractButton *escapeButton() const;
    void setEscapeButton(QAbstractButton *button /KeepReference/);
    void setEscapeButton(QMessageBox::StandardButton button);
    QAbstractButton *clickedButton() const;
    QString informativeText() const;
    void setInformativeText(const QString &text);
    QString detailedText() const;
    void setDetailedText(const QString &text);
    void setWindowTitle(const QString &title);
    void setWindowModality(Qt::WindowModality windowModality);
    virtual void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    QList<QAbstractButton *> buttons() const;
    QMessageBox::ButtonRole buttonRole(QAbstractButton *button) const;

signals:
    void buttonClicked(QAbstractButton *button);

public:
%If (Qt_5_1_0 -)
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
%End
%If (Qt_5_1_0 -)
    Qt::TextInteractionFlags textInteractionFlags() const;
%End
%If (Qt_5_2_0 -)
    void setCheckBox(QCheckBox *cb);
%End
%If (Qt_5_2_0 -)
    QCheckBox *checkBox() const;
%End
};

QFlags<QMessageBox::StandardButton> operator|(QMessageBox::StandardButton f1, QFlags<QMessageBox::StandardButton> f2);
