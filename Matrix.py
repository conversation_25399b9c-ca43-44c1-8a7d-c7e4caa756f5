import pandas as pd
import os
from datetime import datetime, date, timedelta
import time
import glob
from dotenv import load_dotenv
import random
import numpy as np  # Add numpy import for calculations
from dotenv import load_dotenv
load_dotenv()

def calculate_softmax_weights(sharpe, calmar, weight_sharpe=0.5, weight_calmar=0.5):
    """
    Calculate softmax-normalized weights for Sharpe and Calmar ratios.
    """
    if not np.isfinite(sharpe): sharpe = 0
    if not np.isfinite(calmar): calmar = 0
    
    log_scaled_sharpe = np.log1p(max(sharpe, 0))
    log_scaled_calmar = np.log1p(max(calmar, 0))
    weighted_sharpe = weight_sharpe * log_scaled_sharpe
    weighted_calmar = weight_calmar * log_scaled_calmar
    weighted_values = np.array([weighted_sharpe, weighted_calmar])
    exp_values = np.exp(weighted_values)
    softmax_values = exp_values / np.sum(exp_values)
    # Extract Softmax results
    softmax_sharpe = softmax_values[0]
    softmax_calmar = softmax_values[1]
    return softmax_sharpe, softmax_calmar

def calculate_penalty(max_dd, threshold=0.05):
    #print("Max_DD:", max_dd)
    """
    Calculate a penalty based on the maximum drawdown exceeding certain thresholds.
    """
    if max_dd <= 0.05:
        return 10
    elif max_dd <= 0.10:
        return 50
    elif max_dd <= 0.15:
        return 100
    elif max_dd <= 0.20:
        return 200
    elif max_dd <= 0.25:
        return 500
    elif max_dd <= 0.30:
        return 1000
    else:
        return 2000

def calculate_sharpe_ratio(df, risk_free_rate=6.95, annualization_factor=52, use_weighted=False): 
    """
    Calculate the annualized Sharpe Ratio for a series of daily PnL values.
    """
    
    if use_weighted and 'weighted_pnl' in df.columns:
        df['pnl'] = pd.to_numeric(df['weighted_pnl'], errors='coerce')
    else:
        df['pnl'] = pd.to_numeric(df['pnl'], errors='coerce')
        
    returns = df['pnl']  
    print("Returns in calculate_sharpe_ratio:", returns)
    Capital = int(os.getenv('Capital'))
    print("Capital in calculate_sharpe_ratio:", Capital)

    mean_return = returns.mean()
    print("Mean Return in calculate_sharpe_ratio:", mean_return)
    std_dev = returns.std(ddof=1)
    print("Standard Deviation in calculate_sharpe_ratio:", std_dev)

    excess_return = (mean_return / Capital) - (risk_free_rate / annualization_factor)
    print("Excess Return in calculate_sharpe_ratio:", excess_return)
    sharpe_ratio = excess_return / std_dev if std_dev != 0 else 0
    annualized_sharpe_ratio = sharpe_ratio * np.sqrt(annualization_factor)

    return annualized_sharpe_ratio

def calculate_top_drawdowns(cumulative_pnl, dates, top_n=10):
    """
    Calculate the top N drawdowns for a cumulative PnL series.
    """
    # Calculate the running maximum
    running_max = np.maximum.accumulate(cumulative_pnl)
    
    # Calculate drawdowns as a fraction
    drawdowns = (running_max - cumulative_pnl) / running_max
    drawdowns[np.isnan(drawdowns)] = 0  # Handle cases where running_max is 0

    # Initialize lists to store drawdown information
    drawdown_percentages = []
    drawdown_start_dates = []
    drawdown_end_dates = []

    # Find all drawdown periods
    i = 0
    while i < len(drawdowns):
        if drawdowns[i] > 0:  # A drawdown starts
            start_index = i
            while i < len(drawdowns) and drawdowns[i] > 0:  # Find the end of this drawdown
                i += 1
            end_index = i - 1
            
            # Get drawdown percentage
            drawdown_percentage = drawdowns[start_index:end_index + 1].max() * 100
            
            # Find the start date: last occurrence of the running max before the drawdown
            drawdown_peak_index = np.where(cumulative_pnl[:start_index + 1] == running_max[start_index])[0][-1]
            drawdown_start_date = dates[drawdown_peak_index]
            
            # Find the trough date: the lowest PnL during the drawdown
            drawdown_trough_index = np.argmin(cumulative_pnl[start_index:end_index + 1]) + start_index
            drawdown_end_date = dates[drawdown_trough_index]

            # Store the drawdown details
            drawdown_percentages.append(drawdown_percentage)
            drawdown_start_dates.append(drawdown_start_date)
            drawdown_end_dates.append(drawdown_end_date)
        else:
            i += 1

    # Create a DataFrame with the drawdowns
    drawdowns_df = pd.DataFrame({
        "Drawdown Percentage (%)": drawdown_percentages,
        "Start Date": drawdown_start_dates,
        "End (Trough) Date": drawdown_end_dates
    })

    # Sort the drawdowns by percentage in descending order and pick the top N
    drawdowns_df = drawdowns_df.sort_values(by="Drawdown Percentage (%)", ascending=False).head(top_n)

    return drawdowns_df

def max_drawdown(cumulative_pnl, dates):
    """
    Calculate the maximum drawdown, its start date, and end date (trough date) 
    for a given cumulative PnL series.
    """
    # Calculate the running maximum
    running_max = np.maximum.accumulate(cumulative_pnl)
    
    # Calculate drawdowns
    drawdowns = (running_max - cumulative_pnl) / running_max
    #drawdowns[np.isnan(drawdowns)] = 0  # Handle cases where running_max is 0
    
    # Index of the maximum drawdown
    max_dd_index = np.argmax(drawdowns)  # This is the point of the largest drawdown
    max_dd = drawdowns[max_dd_index]
    
    # Find the start date for the maximum drawdown
    max_dd_start_indices = np.where(cumulative_pnl[:max_dd_index] == running_max[max_dd_index])[0]
    if max_dd_start_indices.size > 0:
        max_dd_start_index = max_dd_start_indices[-1]
    else:
        max_dd_start_index = 0  # Handle the case where no start index is found

    max_dd_start_date = dates[max_dd_start_index]
    
    # Find the trough date where the cumulative PnL is at its lowest during the drawdown
    trough_index = np.argmin(cumulative_pnl[max_dd_start_index:max_dd_index + 1]) + max_dd_start_index
    max_dd_end_date = dates[trough_index]

    return max_dd, max_dd_start_date, max_dd_end_date

def calculate_calmar_ratio(returns, cumulative_pnl, dates, risk_free_rate=6.95, annualization_factor=52):
    """
    Calculate the Calmar Ratio for a trading strategy.
    """
    print ("Returns in calculate_calmar_ratio:", returns)
    # Validate inputs
    if len(returns) == 0:
        raise ValueError("The returns array is empty. Cannot calculate Calmar Ratio.")
    

    # Annualized return
    mean_return = np.mean(returns)
    print("Mean Return in calculate_calmar_ratio:", mean_return)
    annualized_return = mean_return * annualization_factor
    
    # Maximum drawdown
    max_dd, max_dd_start_date, max_dd_end_date = max_drawdown(cumulative_pnl, dates)
    
    # Handle edge cases for drawdown
    if max_dd == 0:
        print("Warning: Maximum drawdown is zero. Returning zero for Calmar Ratio.")
        return (0, 0, 0, 0)
    else:
        # Calculate Calmar Ratio
        calmar_ratio = (annualized_return - risk_free_rate) / max_dd
        return calmar_ratio, max_dd, max_dd_start_date, max_dd_end_date

def calculate_performance_metrics(df, equity_col='equity'):
    """Calculate all performance metrics at once to avoid redundant calculations"""
       
    # Drop rows with missing PnL or Equity
    df = df.dropna(subset=['pnl', equity_col])
    print(df)

    returns = df['pnl'].values
    dates = df['date'].values
    cumulative_pnl = df[equity_col].values  # Use specified equity column
    
    # Use weighted returns if weighted_equity column is being used
    if equity_col == 'weighted_equity' and 'weighted_pnl' in df.columns:
        weighted_returns = df['weighted_pnl'].values
        use_weighted_sharpe = True
    else:
        weighted_returns = returns
        use_weighted_sharpe = False
    
    # Calculate Sharpe ratio with weighted flag
    sharpe = calculate_sharpe_ratio(df, use_weighted=use_weighted_sharpe)
    
    # Calculate Calmar ratio and max drawdown using weighted returns
    calmar, max_dd, max_dd_start, max_dd_end = calculate_calmar_ratio(
        weighted_returns, cumulative_pnl, dates
    )
    
    # Calculate top drawdowns
    top_drawdowns = calculate_top_drawdowns(cumulative_pnl, dates)
    
    return {
        'sharpe_ratio': sharpe,
        'calmar_ratio': calmar,
        'max_drawdown': max_dd,
        'max_dd_start': max_dd_start,
        'max_dd_end': max_dd_end,
        'top_drawdowns': top_drawdowns
    }
    
def process_performance_metrics(df, equity_col='equity'):
    """
    Process and print performance metrics from the given DataFrame.
    
    Parameters:
    df (DataFrame): DataFrame containing 'date', 'pnl', and equity columns.
    equity_col (str): Name of the equity column to use for calculations.
    """
    # Ensure the DataFrame contains the necessary columns
    if 'pnl' not in df.columns or 'date' not in df.columns or equity_col not in df.columns:
        raise ValueError(f"The DataFrame must contain 'date', 'pnl', and '{equity_col}' columns.")

    # Calculate performance metrics
    performance_metrics = calculate_performance_metrics(df, equity_col)

    # Print the results
    print("Performance Metrics:")
    #print(f"Sharpe Ratio: {performance_metrics['sharpe_ratio']}")
    #print(f"Calmar Ratio: {performance_metrics['calmar_ratio']}")
    print(f"Max Drawdown: {performance_metrics['max_drawdown']}")
    print(f"Max Drawdown Start Date: {performance_metrics['max_dd_start']}")
    print(f"Max Drawdown End Date: {performance_metrics['max_dd_end']}")
    print("Top Drawdowns:")
    print(performance_metrics['top_drawdowns'])
    
    return performance_metrics
