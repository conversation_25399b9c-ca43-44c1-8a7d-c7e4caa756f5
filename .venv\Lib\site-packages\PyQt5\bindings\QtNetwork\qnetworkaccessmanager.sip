// qnetworkaccessmanager.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkAccessManager : QObject
{
%TypeHeaderCode
#include <qnetworkaccessmanager.h>
%End

public:
    enum Operation
    {
        HeadOperation,
        GetOperation,
        PutOperation,
        PostOperation,
        DeleteOperation,
        CustomOperation,
    };

    explicit QNetworkAccessManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNetworkAccessManager();
    QNetworkProxy proxy() const;
    void setProxy(const QNetworkProxy &proxy);
    QNetworkCookieJar *cookieJar() const;
    void setCookieJar(QNetworkCookieJar *cookieJar /Transfer/);
    QNetworkReply *head(const QNetworkRequest &request);
    QNetworkReply *get(const QNetworkRequest &request);
    QNetworkReply *post(const QNetworkRequest &request, QIODevice *data);
    QNetworkReply *post(const QNetworkRequest &request, const QByteArray &data);
    QNetworkReply *post(const QNetworkRequest &request, QHttpMultiPart *multiPart);
    QNetworkReply *put(const QNetworkRequest &request, QIODevice *data);
    QNetworkReply *put(const QNetworkRequest &request, const QByteArray &data);
    QNetworkReply *put(const QNetworkRequest &request, QHttpMultiPart *multiPart);

signals:
    void proxyAuthenticationRequired(const QNetworkProxy &proxy, QAuthenticator *authenticator);
    void authenticationRequired(QNetworkReply *reply, QAuthenticator *authenticator);
    void finished(QNetworkReply *reply);
%If (Qt_5_1_0 -)
%If (PyQt_SSL)
    void encrypted(QNetworkReply *reply);
%End
%End
%If (PyQt_SSL)
    void sslErrors(QNetworkReply *reply, const QList<QSslError> &errors);
%End
    void networkAccessibleChanged(QNetworkAccessManager::NetworkAccessibility accessible);
%If (Qt_5_5_0 -)
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QNetworkReply *reply, QSslPreSharedKeyAuthenticator *authenticator);
%End
%End

protected:
    virtual QNetworkReply *createRequest(QNetworkAccessManager::Operation op, const QNetworkRequest &request, QIODevice *device = 0) /AbortOnException,DisallowNone,ReleaseGIL/;

public:
    QNetworkProxyFactory *proxyFactory() const;
    void setProxyFactory(QNetworkProxyFactory *factory /Transfer/);
    QAbstractNetworkCache *cache() const;
    void setCache(QAbstractNetworkCache *cache /Transfer/);
    QNetworkReply *deleteResource(const QNetworkRequest &request);

    enum NetworkAccessibility
    {
        UnknownAccessibility,
        NotAccessible,
        Accessible,
    };

    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, QIODevice *data = 0);
%If (Qt_5_8_0 -)
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, const QByteArray &data);
%End
%If (Qt_5_8_0 -)
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, QHttpMultiPart *multiPart);
%End
    void setConfiguration(const QNetworkConfiguration &config);
    QNetworkConfiguration configuration() const;
    QNetworkConfiguration activeConfiguration() const;
    void setNetworkAccessible(QNetworkAccessManager::NetworkAccessibility accessible);
    QNetworkAccessManager::NetworkAccessibility networkAccessible() const;
    void clearAccessCache();
%If (Qt_5_2_0 -)
    QStringList supportedSchemes() const;
%End
%If (Qt_5_2_0 -)
%If (PyQt_SSL)
    void connectToHostEncrypted(const QString &hostName, quint16 port = 443, const QSslConfiguration &sslConfiguration = QSslConfiguration::defaultConfiguration());
%End
%End
%If (Qt_5_13_0 -)
%If (PyQt_SSL)
    void connectToHostEncrypted(const QString &hostName, quint16 port, const QSslConfiguration &sslConfiguration, const QString &peerName);
%End
%End
%If (Qt_5_2_0 -)
    void connectToHost(const QString &hostName, quint16 port = 80);
%End

protected slots:
%If (Qt_5_2_0 -)
    QStringList supportedSchemesImplementation() const;
%End

public:
%If (Qt_5_9_0 -)
    void clearConnectionCache();
%End
%If (Qt_5_9_0 -)
    void setStrictTransportSecurityEnabled(bool enabled);
%End
%If (Qt_5_9_0 -)
    bool isStrictTransportSecurityEnabled() const;
%End
%If (Qt_5_9_0 -)
    void addStrictTransportSecurityHosts(const QVector<QHstsPolicy> &knownHosts);
%End
%If (Qt_5_9_0 -)
    QVector<QHstsPolicy> strictTransportSecurityHosts() const;
%End
%If (Qt_5_9_0 -)
    void setRedirectPolicy(QNetworkRequest::RedirectPolicy policy);
%End
%If (Qt_5_9_0 -)
    QNetworkRequest::RedirectPolicy redirectPolicy() const;
%End
%If (Qt_5_10_0 -)
    void enableStrictTransportSecurityStore(bool enabled, const QString &storeDir = QString());
%End
%If (Qt_5_10_0 -)
    bool isStrictTransportSecurityStoreEnabled() const;
%End
%If (Qt_5_14_0 -)
    bool autoDeleteReplies() const;
%End
%If (Qt_5_14_0 -)
    void setAutoDeleteReplies(bool autoDelete);
%End
%If (Qt_5_15_0 -)
    int transferTimeout() const;
%End
%If (Qt_5_15_0 -)
    void setTransferTimeout(int timeout = QNetworkRequest::TransferTimeoutConstant::DefaultTransferTimeoutConstant);
%End
};
