import os
import dask.dataframe as dd
import pandas as pd
#import logger_config
from dotenv import load_dotenv
from total_pnl import calculate_total_pnl_per_minute
from datetime import datetime

# Load environment variables and logger once
load_dotenv()
#logger = logger_config.get_logger('main_logger')
GLOBAL_QTY = int(os.getenv("GLOBAL_QTY"))
#logger.info(f"GLOBAL_QTY: {GLOBAL_QTY}")
Capital = int(os.getenv("CAPITAL"))
#logger.info(f"Capital: {Capital}")
#STOP_LOSS_PERCENTAGE = float(os.getenv("STOP_LOSS_PERCENTAGE"))
#logger.info(f"STOP_LOSS_PERCENTAGE: {STOP_LOSS_PERCENTAGE}")
#TARGET_PROFIT_PERCENTAGE = float(os.getenv("TARGET_PROFIT_PERCENTAGE"))
#logger.info(f"TARGET_PROFIT_PERCENTAGE: {TARGET_PROFIT_PERCENTAGE}")
#STOP_LOSS = Capital * (STOP_LOSS_PERCENTAGE / 100)
#logger.info(f"STOP_LOSS: {STOP_LOSS}")
#TARGET_PROFIT = Capital * (TARGET_PROFIT_PERCENTAGE / 100)
#logger.info(f"TARGET_PROFIT: {TARGET_PROFIT}")
#ratio_check = float(os.getenv("ratio_check"))
#logger.info(f"ratio_check: {ratio_check}")


def exit_all_positions(positions, exit_time, ce_sell_price=None, pe_sell_price=None, ce_buy_price=None, pe_buy_price=None):
    #logger.info("Exiting all positions for the day...")
    total_pnl = 0

    for key, pos in positions.items():
        if pos.get('closed', False):
            continue

        symbol = pos.get('symbol')
        side = pos.get('side')
        exit_price = None

        if 'CE' in key:
            if side == 'SELL' and ce_sell_price is not None:
                exit_price = ce_sell_price
            elif side == 'BUY' and ce_buy_price is not None:
                exit_price = ce_buy_price
        elif 'PE' in key:
            if side == 'SELL' and pe_sell_price is not None:
                exit_price = pe_sell_price
            elif side == 'BUY' and pe_buy_price is not None:
                exit_price = pe_buy_price

        # Fallback to current price if specific exit price is not available
        if exit_price is None:
            exit_price = pos.get('current_price')

        pos.update({
            'exit_time': exit_time,
            'exit_price': exit_price,
            'closed': True
        })

        entry_price = pos.get('ltp')

        if entry_price is not None and exit_price is not None:
            multiplier = -1 if side == 'SELL' else 1
            pos['pnl'] = (exit_price - entry_price) * multiplier * GLOBAL_QTY
            total_pnl += pos['pnl']
        else:
            pos['pnl'] = None

        #logger.info(
          #  f"Position {key} on {symbol} closed at {exit_time} with exit price {exit_price} | P&L: {pos['pnl']}"
        #)

    #logger.info(f"Total P&L for the day: {total_pnl}")
    #logger.info("All positions marked as closed.")
    return round(total_pnl, 2)


def monitor_prices(positions, folder_path, start_time, exit_time, folder_date,target_profit, stop_loss,ratio_check):
    #logger.info(f"Stop Loss: {STOP_LOSS} | Target Profit: {TARGET_PROFIT}")

    STOP_LOSS_PERCENTAGE = stop_loss
    TARGET_PROFIT_PERCENTAGE = target_profit
    STOP_LOSS = Capital * (STOP_LOSS_PERCENTAGE / 100)
    TARGET_PROFIT = Capital * (TARGET_PROFIT_PERCENTAGE / 100)

    ce_sell_symbol = pe_sell_symbol = ce_buy_symbol = pe_buy_symbol = None
    for key, pos in positions.items():
        if pos['side'] == 'SELL':
            if 'CE' in key: ce_sell_symbol = pos['symbol']
            elif 'PE' in key: pe_sell_symbol = pos['symbol']
        if pos['side'] == 'BUY':
            if 'CE' in key: ce_buy_symbol = pos['symbol']
            elif 'PE' in key: pe_buy_symbol = pos['symbol']    

    if not ce_sell_symbol or not pe_sell_symbol or not ce_buy_symbol or not pe_buy_symbol:
        #logger.info("Either CE_SELL, PE_SELL, CE_BUY, or PE_BUY symbol not found in positions.")
        return

    def load_option_data(symbol, subfolder, folder_date):
        file_path = os.path.join(folder_path, subfolder, f"{symbol}.parquet")
        if not os.path.exists(file_path):
            #logger.info(f"File not found: {file_path}")
            return None

        try:
            # Read only necessary columns
            df = dd.read_parquet(file_path)
            # Filter by date without triggering compute
            filtered_df = df[df['YMD'] == int(folder_date)]
            #if len(filtered_df.head(1)) == 0:
                #logger.info(f"No data available for date: {folder_date}")
                
            # Compute to pandas for time filtering
            df = filtered_df.compute().copy()
            # Use .loc to assign the new column
            df.loc[:, 'time_only'] = pd.to_datetime(df['Time'], format='%H:%M').dt.time
            
            # Time filtering
            return df[
                (df['time_only'] >= start_time) &
                (df['time_only'] <= exit_time)
            ][['YMD', 'time_only', 'Close']]

        except Exception as e:
            #logger.error(f"Error reading Parquet file {file_path}: {e}")
            return None


    ce_sell_df = load_option_data(ce_sell_symbol, "CE_SELL", folder_date)
    if ce_sell_df is None:
        #logger.info(f"Data for CE_SELL option {ce_sell_symbol} not available.")
        return
    pe_sell_df = load_option_data(pe_sell_symbol, "PE_SELL", folder_date)
    if pe_sell_df is None:
        #logger.info(f"Data for PE_SELL option {pe_sell_symbol} not available.")
        return
    ce_buy_df = load_option_data(ce_buy_symbol, "CE_BUY", folder_date)
    if ce_buy_df is None:
        #logger.info(f"Data for CE_BUY option {ce_buy_symbol} not available.")
        return
    pe_buy_df = load_option_data(pe_buy_symbol, "PE_BUY", folder_date)
    if pe_buy_df is None:
        #logger.info(f"Data for PE_BUY option {pe_buy_symbol} not available.")
        return

    if ce_sell_df is None or pe_sell_df is None or ce_buy_df is None or pe_buy_df is None:
        #logger.info("Data for one or more options not available.")
        return

    # Merge operation using dask
    ce_sell_ddf = dd.from_pandas(ce_sell_df.rename(columns={'Close': 'CE_Sell_Close'}), npartitions=1)
    pe_sell_ddf = dd.from_pandas(pe_sell_df.rename(columns={'Close': 'PE_Sell_Close'}), npartitions=1)
    ce_buy_ddf = dd.from_pandas(ce_buy_df.rename(columns={'Close': 'CE_Buy_Close'}), npartitions=1)
    pe_buy_ddf = dd.from_pandas(pe_buy_df.rename(columns={'Close': 'PE_Buy_Close'}), npartitions=1)

   # First merge CE and PE sell
    merged_df = dd.merge(
        ce_sell_ddf,
        pe_sell_ddf,
        on=['YMD', 'time_only'],
        how='inner'
    )

    # Then merge the result with CE buy
    merged_df = dd.merge(
        merged_df,
        ce_buy_ddf,
        on=['YMD', 'time_only'],
        how='inner'
    )

    # Finally merge with PE buy
    merged_df = dd.merge(
        merged_df,
        pe_buy_ddf,
        on=['YMD', 'time_only'],
        how='inner'
    )

    # Calculate the ratio column
    merged_df['lower_ltp'] = merged_df[['CE_Sell_Close', 'PE_Sell_Close']].min(axis=1)
    merged_df['higher_ltp'] = merged_df[['CE_Sell_Close', 'PE_Sell_Close']].max(axis=1)
    merged_df['ratio'] = (merged_df['lower_ltp'] / merged_df['higher_ltp']).round(2)

    #logger.info("\nDate\t\tTime\t\tCE_SELL Close\tPE_SELL Close\tlower_ltp\thigher_ltp\tratio")
    #logger.info("-" * 50)

    for _, row in merged_df.iterrows():
        current_time = datetime.strptime(str(row['time_only'])[:5], "%H:%M").time()
        ce_sell_close = row['CE_Sell_Close']
        pe_sell_close = row['PE_Sell_Close']
        ce_buy_close = row['CE_Buy_Close']
        pe_buy_close = row['PE_Buy_Close']
                
        #logger.info(f"{row['YMD']}\t{current_time}\t{ce_close}\t{pe_close}\t{row['lower_ltp']}\t{row['higher_ltp']}\t{row['ratio']}")
        
        total_pnl = round(calculate_total_pnl_per_minute(positions, ce_sell_close, pe_sell_close,ce_buy_close, pe_buy_close),2)
        
        # Log types for sanity
        #logger.info(f"ratio={row['ratio']},total_pnl= {total_pnl} and current_time={current_time}")
        
        if  row['ratio'] < ratio_check  or total_pnl <= STOP_LOSS or total_pnl >= TARGET_PROFIT or current_time >= exit_time:

            if  total_pnl >= TARGET_PROFIT:
                #logger.info("Exit condition met: Total PnL is greater than or equal to TARGET_PROFIT.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ), "TARGET_PROFIT"
                        
            elif total_pnl <= STOP_LOSS:
                #logger.info("Exit condition met: Total PnL is less than or equal to STOP_LOSS.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"STOP_LOSS"
                        
            elif row['ratio'] < ratio_check:
                #logger.info(f"Exit condition met: Ratio is less than {ratio_check}.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"RATIO_HIT"
            
            elif current_time >= exit_time:
                #logger.info("Exit condition met: Current time is greater than or equal to exit time.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"TIME_EXIT"
        #else:
            #logger.info("Exit condition not met. Waiting for the next check.")
    return None, "NO_EXIT_CONDITION_MET"        