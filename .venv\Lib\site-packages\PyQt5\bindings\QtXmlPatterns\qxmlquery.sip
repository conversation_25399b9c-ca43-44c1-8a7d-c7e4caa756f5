// qxmlquery.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlQuery
{
%TypeHeaderCode
#include <qxmlquery.h>
%End

%TypeCode
// Needed by the evaluateToStringList() %MethodCode.
#include <qstringlist.h>
%End

public:
    enum QueryLanguage
    {
        XQuery10,
        XSLT20,
    };

    QXmlQuery();
    QXmlQuery(const QXmlQuery &other);
    QXmlQuery(const QXmlNamePool &np);
    QXmlQuery(QXmlQuery::QueryLanguage queryLanguage, const QXmlNamePool &pool = QXmlNamePool());
    ~QXmlQuery();
    void setMessageHandler(QAbstractMessageHandler *messageHandler /KeepReference/);
    QAbstractMessageHandler *messageHandler() const;
    void setQuery(const QString &sourceCode, const QUrl &documentUri = QUrl());
    void setQuery(QIODevice *sourceCode, const QUrl &documentUri = QUrl());
    void setQuery(const QUrl &queryURI, const QUrl &baseUri = QUrl());
    QXmlNamePool namePool() const;
    void bindVariable(const QXmlName &name, const QXmlItem &value);
    void bindVariable(const QXmlName &name, QIODevice *);
    void bindVariable(const QXmlName &name, const QXmlQuery &query);
    void bindVariable(const QString &localName, const QXmlItem &value);
    void bindVariable(const QString &localName, QIODevice *);
    void bindVariable(const QString &localName, const QXmlQuery &query);
    bool isValid() const;
    void evaluateTo(QXmlResultItems *result) const;
    bool evaluateTo(QAbstractXmlReceiver *callback) const;
    SIP_PYOBJECT evaluateToStringList() const /TypeHint="QStringList"/;
%MethodCode
        static const sipTypeDef *sipType_QStringList = 0;
        
        if (!sipType_QStringList)
            sipType_QStringList = sipFindType("QStringList");
        
        bool ok;
        QStringList *result = new QStringList;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->evaluateTo(result);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            sipRes = sipConvertFromNewType(result, sipType_QStringList, NULL);
        }
        else
        {
            delete result;
            sipRes = Py_None;
            Py_INCREF(Py_None);
        }
%End

    bool evaluateTo(QIODevice *target) const;
    SIP_PYOBJECT evaluateToString() const /TypeHint="QString"/;
%MethodCode
        static const sipTypeDef *sipType_QStringList = 0;
        
        if (!sipType_QStringList)
            sipType_QStringList = sipFindType("QStringList");
        
        bool ok;
        QString *result = new QString;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->evaluateTo(result);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            sipRes = sipConvertFromNewType(result, sipType_QString, NULL);
        }
        else
        {
            delete result;
            sipRes = Py_None;
            Py_INCREF(Py_None);
        }
%End

    void setUriResolver(const QAbstractUriResolver *resolver /KeepReference/);
    const QAbstractUriResolver *uriResolver() const;
    void setFocus(const QXmlItem &item);
    bool setFocus(const QUrl &documentURI);
    bool setFocus(QIODevice *document);
    bool setFocus(const QString &focus);
    void setInitialTemplateName(const QXmlName &name);
    void setInitialTemplateName(const QString &name);
    QXmlName initialTemplateName() const;
    void setNetworkAccessManager(QNetworkAccessManager *newManager /KeepReference/);
    QNetworkAccessManager *networkAccessManager() const;
    QXmlQuery::QueryLanguage queryLanguage() const;
};
