// qcameraimageprocessingcontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraImageProcessingControl : QMediaControl
{
%TypeHeaderCode
#include <qcameraimageprocessingcontrol.h>
%End

public:
    virtual ~QCameraImageProcessingControl();

    enum ProcessingParameter
    {
        WhiteBalancePreset,
        ColorTemperature,
        Contrast,
        Saturation,
        Brightness,
        Sharpening,
        Denoising,
        ContrastAdjustment,
        SaturationAdjustment,
        BrightnessAdjustment,
        SharpeningAdjustment,
        DenoisingAdjustment,
        ColorFilter,
        ExtendedParameter,
    };

    virtual bool isParameterSupported(QCameraImageProcessingControl::ProcessingParameter) const = 0;
    virtual bool isParameterValueSupported(QCameraImageProcessingControl::ProcessingParameter parameter, const QVariant &value) const = 0;
    virtual QVariant parameter(QCameraImageProcessingControl::ProcessingParameter parameter) const = 0;
    virtual void setParameter(QCameraImageProcessingControl::ProcessingParameter parameter, const QVariant &value) = 0;

protected:
    explicit QCameraImageProcessingControl(QObject *parent /TransferThis/ = 0);
};
