import pandas as pd

# Load the first CSV (with datetime and volume)
df1 = pd.read_csv('INDIA VIX_minute.csv')

# Parse the date column from df1
df1['date'] = pd.to_datetime(df1['date'], format='%d-%m-%Y %H:%M')

# Drop volume column
df1 = df1[['date', 'open', 'high', 'low', 'close']]

# Load the second CSV (daily VIX data)
df2 = pd.read_csv('hist_india_vix_-01-01-2025-to-03-07-2025.csv')

# Normalize column names
df2.columns = [col.strip().lower() for col in df2.columns]

# Parse and format the date, then add dummy time "09:15"
df2['date'] = pd.to_datetime(df2['date'], format='%d-%b-%y')
df2['date'] = df2['date'].dt.strftime('%d-%m-%Y') + ' 09:15'
df2['date'] = pd.to_datetime(df2['date'], format='%d-%m-%Y %H:%M')

# Filter df2 to only include dates after the last date in df1
cutoff = df1['date'].max()
df2 = df2[df2['date'] > cutoff]

# Select and reorder columns
df2 = df2[['date', 'open', 'high', 'low', 'close']]

# Merge dataframes
merged_df = pd.concat([df1, df2], ignore_index=True)

# Sort by date
merged_df = merged_df.sort_values(by='date').reset_index(drop=True)

# Format date column back to string: 'dd-mm-yyyy HH:MM'
merged_df['date'] = merged_df['date'].dt.strftime('%d-%m-%Y %H:%M')

# Save to CSV
merged_df.to_csv('Merged_INDIA VIX_minute.csv', index=False)

print("✅ Merged file saved as 'Merged_INDIA VIX_minute.csv' with correct format and no volume.")
