
import struct
# data = b'\x00\x04L}\x145D\x8b`7FO\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07Q\xb6\x00\x00\x00\x00\x00\x05\xae(\x00\x00RS\x00\x00\x01,\x00\x00\x00\x01\x00\x00RN\x00\x00\x1f\xa7\x00\x00\x00\x07\x00\x00RI\x00\x00\x10.\x00\x00\x00\x0e\x00\x00RD\x00\x00\x1a\xfc\x00\x00\x00\r\x00\x00R?\x00\x00\t\x8c\x00\x00\x00\t\x00\x00R]\x00\x00\x01\x92\x00\x00\x00\x03\x00\x00Rb\x00\x00\x1d\xa1\x00\x00\x00\x0c\x00\x00Rg\x00\x00E\x91\x00\x00\x00\n\x00\x00Rl\x00\x00%\xc9\x00\x00\x00\x15\x00\x00Rq\x00\x00\x10h\x00\x00\x00\t'
data = b'\x00\x04L}\x145D\x8b`7FO\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07Q\xb6\x00\x00\x00\x00\x00\x05\xadO\x00\x00RS\x00\x00\x01,\x00\x00\x00\x01\x00\x00RN\x00\x00\x1f\xa7\x00\x00\x00\x07\x00\x00RI\x00\x00\x13\xb8\x00\x00\x00\x0f\x00\x00RD\x00\x00\x1a\xfc\x00\x00\x00\r\x00\x00R?\x00\x00\x11Z\x00\x00\x00\n\x00\x00R]\x00\x00\x01\x92\x00\x00\x00\x03\x00\x00Rb\x00\x00\x1b\x85\x00\x00\x00\x0c\x00\x00Rg\x00\x00E-\x00\x00\x00\t\x00\x00Rl\x00\x00%\x80\x00\x00\x00\x15\x00\x00Rq\x00\x00\x12X\x00\x00\x00\n'
# data = b'\x00\x04L}\x145D\x8b`7FP\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07T\xae\x00\x00\x00\x00\x00\x05\xb3f\x00\x00RS\x00\x00\x05\xfb\x00\x00\x00\x03\x00\x00RN\x00\x00 z\x00\x00\x00\r\x00\x00RI\x00\x00\x12\xdc\x00\x00\x00\x0b\x00\x00RD\x00\x00\x1b\x06\x00\x00\x00\x0e\x00\x00R?\x00\x00\x111\x00\x00\x00\t\x00\x00R]\x00\x00\x00G\x00\x00\x00\x01\x00\x00Rb\x00\x00\x19\xc1\x00\x00\x00\x08\x00\x00Rg\x00\x00NS\x00\x00\x00\x0e\x00\x00Rl\x00\x00%\x80\x00\x00\x00\x15\x00\x00Rq\x00\x00\x12X\x00\x00\x00\n\x00\x04L}\x145D\x8b`7FQ\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07UT\x00\x00\x00\x00\x00\x05\xbbU\x00\x00RS\x00\x00\x04\xcf\x00\x00\x00\x02\x00\x00RN\x00\x00\x1d\x86\x00\x00\x00\x0c\x00\x00RI\x00\x00\x15\xbc\x00\x00\x00\x0c\x00\x00RD\x00\x00\x1a\xfc\x00\x00\x00\r\x00\x00R?\x00\x00\x13!\x00\x00\x00\n\x00\x00R]\x00\x00\x06\x87\x00\x00\x00\x03\x00\x00Rb\x00\x00\x1aO\x00\x00\x00\t\x00\x00Rg\x00\x00M\xc5\x00\x00\x00\r\x00\x00Rl\x00\x00%\x80\x00\x00\x00\x15\x00\x00Rq\x00\x00\x12X\x00\x00\x00\n'
# data = b'\x00\x04L}\x145D\x8b`7FQ\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07UT\x00\x00\x00\x00\x00\x05\xbbU\x00\x00RS\x00\x00\x04\xcf\x00\x00\x00\x02\x00\x00RN\x00\x00\x1d\x86\x00\x00\x00\x0c\x00\x00RI\x00\x00\x15\xbc\x00\x00\x00\x0c\x00\x00RD\x00\x00\x1a\xfc\x00\x00\x00\r\x00\x00R?\x00\x00\x13!\x00\x00\x00\n\x00\x00R]\x00\x00\x06\x87\x00\x00\x00\x03\x00\x00Rb\x00\x00\x1aO\x00\x00\x00\t\x00\x00Rg\x00\x00M\xc5\x00\x00\x00\r\x00\x00Rl\x00\x00%\x80\x00\x00\x00\x15\x00\x00Rq\x00\x00\x12X\x00\x00\x00\n'
# data = b'\x00\x04L}\x145D\x8b`7FR\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07UT\x00\x00\x00\x00\x00\x05\xba)\x00\x00RS\x00\x00\x04\xcf\x00\x00\x00\x02\x00\x00RN\x00\x00\x1d\x86\x00\x00\x00\x0c\x00\x00RI\x00\x00\x15\xbc\x00\x00\x00\x0c\x00\x00RD\x00\x00\x1a\xfc\x00\x00\x00\r\x00\x00R?\x00\x00\x13!\x00\x00\x00\n\x00\x00R]\x00\x00\x05[\x00\x00\x00\x02\x00\x00Rb\x00\x00\x1aO\x00\x00\x00\t\x00\x00Rg\x00\x00M\xc5\x00\x00\x00\r\x00\x00Rl\x00\x00%\x80\x00\x00\x00\x15\x00\x00Rq\x00\x00\x12X\x00\x00\x00\n\x00\x04L}\x145D\x8b`7FR\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00RS\x00\x00Q\xf4\x00\x00R\x80\x00\x00Q\xcc\x00\x00Q\x90\x00\x00RN\x00\x00RS\x00\x00RN\x00\x00RS\x00\x00\x00\x00\x00\x00\x03\xe8\x00\x00\x02\xbc`6\xf8\xe7\x00\x00\x00\x00\x00\x19\xb0K\x00\x00\x00\x00\x00\x07S\x99\x00\x00\x00\x00\x00\x05\xb1.\x00\x00RS\x00\x00\x05\x8c\x00\x00\x00\x03\x00\x00RN\x00\x00\x1c\xf1\x00\x00\x00\x0b\x00\x00RI\x00\x00\x15\xbc\x00\x00\x00\x0c\x00\x00RD\x00\x00\x1b\t\x00\x00\x00\x0e\x00\x00R?\x00\x00\x111\x00\x00\x00\t\x00\x00R]\x00\x00\x01s\x00\x00\x00\x02\x00\x00Rb\x00\x00\x15\xca\x00\x00\x00\x07\x00\x00Rg\x00\x00N\xdc\x00\x00\x00\r\x00\x00Rl\x00\x00#\xc9\x00\x00\x00\x13\x00\x00Rq\x00\x00\x12j\x00\x00\x00\x0b'

# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x97\xfc\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd4\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x00\x00\x00\x00\x02\xca\x00\x00\x00\x02`8\x97\xfc\x00\x00\x9a\xd6\x01\xbe\x05m\x00\x00\x00\x00\x00)\xeb\xc7\x00\x00\x00\x00\x002\x0f\x9b\x00\x00\x99\xd4\x00\x00\x04/\x00\x00\x00\x04\x00\x00\x99\xcf\x00\x00\x11\x17\x00\x00\x00\n\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x0b=\x00\x00\x00\x11\x00\x00\x99\xc0\x00\x00\n\xb8\x00\x00\x00\x12\x00\x00\x99\xd9\x00\x00\x01t\x00\x00\x00\x02\x00\x00\x99\xde\x00\x00\t&\x00\x00\x00\r\x00\x00\x99\xe3\x00\x00\x17\x10\x00\x00\x00\x1d\x00\x00\x99\xe8\x00\x00\\\xf4\x00\x00\x00l\x00\x00\x99\xed\x00\x00\x1aS\x00\x00\x00\x10'
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x97\xfc\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd9\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x00\x00\x00\x00\x03\x0b\x00\x00\x00#`8\x97\xfc\x00\x00\x9a\xd6\x01\xbe\x05\xae\x00\x00\x00\x00\x00)\xeb\xa9\x00\x00\x00\x00\x002\x0f\xe0\x00\x00\x99\xd4\x00\x00\x04\x11\x00\x00\x00\x04\x00\x00\x99\xcf\x00\x00\x11\x17\x00\x00\x00\n\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x0b=\x00\x00\x00\x11\x00\x00\x99\xc0\x00\x00\n\xb8\x00\x00\x00\x12\x00\x00\x99\xd9\x00\x00\x01R\x00\x00\x00\x03\x00\x00\x99\xde\x00\x00\t)\x00\x00\x00\x0e\x00\x00\x99\xe3\x00\x00\x17\x10\x00\x00\x00\x1d\x00\x00\x99\xe8\x00\x00\\\xf4\x00\x00\x00l\x00\x00\x99\xed\x00\x00\x1aS\x00\x00\x00\x10\x00\x00\t/\x96\xe9\x93\xe5`8\x97\xfd\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd4\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x00\x00\x00\x00\x05\x19\x00\x00\x01\x9a`8\x97\xfd\x00\x00\x9a\xd6\x01\xbe\x07\xbc\x00\x00\x00\x00\x00)\xedy\x00\x00\x00\x00\x002\x14\x17\x00\x00\x99\xd4\x00\x00\x02|\x00\x00\x00\x05\x00\x00\x99\xcf\x00\x00\x11\x17\x00\x00\x00\n\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x0c\x96\x00\x00\x00\x0f\x00\x00\x99\xc0\x00\x00\x0c\xc2\x00\x00\x00\x15\x00\x00\x99\xd9\x00\x00\x00\x93\x00\x00\x00\x02\x00\x00\x99\xde\x00\x00\t\x10\x00\x00\x00\r\x00\x00\x99\xe3\x00\x00\x17\x10\x00\x00\x00\x1d\x00\x00\x99\xe8\x00\x00Z\x15\x00\x00\x00m\x00\x00\x99\xed\x00\x00\x1ds\x00\x00\x00\x11'
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x97\xfe\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd9\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x00\x00\x00\x00\x07n\x00\x00\x00\n`8\x97\xfe\x00\x00\x9a\xd6\x01\xbe\n\x11\x00\x00\x00\x00\x00)\xeb\xa7\x00\x00\x00\x00\x002\x1br\x00\x00\x99\xd4\x00\x00\x00\x8d\x00\x00\x00\x05\x00\x00\x99\xcf\x00\x00\x11!\x00\x00\x00\x0b\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x0e\x9f\x00\x00\x00\x11\x00\x00\x99\xc0\x00\x00\r\xd8\x00\x00\x00\x13\x00\x00\x99\xd9\x00\x00\x00\xf1\x00\x00\x00\x03\x00\x00\x99\xde\x00\x00\x07/\x00\x00\x00\x0b\x00\x00\x99\xe3\x00\x00\x19\n\x00\x00\x00 \x00\x00\x99\xe8\x00\x00Y\xa3\x00\x00\x00l\x00\x00\x99\xed\x00\x00\x1b~\x00\x00\x00\x0f'
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x97\xff\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd4\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x00\x00\x00\x00\x07\xea\x00\x00\x00\x08`8\x97\xff\x00\x00\x9a\xd6\x01\xbe\n\x8d\x00\x00\x00\x00\x00)\xde\x8b\x00\x00\x00\x00\x002\x1cP\x00\x00\x99\xd4\x00\x00\x00]\x00\x00\x00\x04\x00\x00\x99\xcf\x00\x00\x11!\x00\x00\x00\x0b\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x06c\x00\x00\x00\n\x00\x00\x99\xc0\x00\x00\x0f\x91\x00\x00\x00\x18\x00\x00\x99\xd9\x00\x00\x00\xff\x00\x00\x00\x04\x00\x00\x99\xde\x00\x00\t,\x00\x00\x00\x0e\x00\x00\x99\xe3\x00\x00\x19\t\x00\x00\x00\x1f\x00\x00\x99\xe8\x00\x00W\xaf\x00\x00\x00k\x00\x00\x99\xed\x00\x00\x1b~\x00\x00\x00\x0f'
# data = b"\x00\x00\t/\x96\xe9\x93\xe5`8\x98\x00\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd4\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd4\x00\x00\x00\x00\x00\x00\x08\x8a\x00\x00\x00\x07`8\x98\x00\x00\x00\x9a\xd6\x01\xbe\x0b-\x00\x00\x00\x00\x00)\xed\xf6\x00\x00\x00\x00\x002\x10'\x00\x00\x99\xd4\x00\x00\x01\n\x00\x00\x00\x05\x00\x00\x99\xcf\x00\x00\x11!\x00\x00\x00\x0b\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x0f$\x00\x00\x00\x11\x00\x00\x99\xc0\x00\x00\n\xb8\x00\x00\x00\x12\x00\x00\x99\xd9\x00\x00\x00\x83\x00\x00\x00\x03\x00\x00\x99\xde\x00\x00\x08\x95\x00\x00\x00\x0c\x00\x00\x99\xe3\x00\x00\x19\xa0\x00\x00\x00!\x00\x00\x99\xe8\x00\x00ch\x00\x00\x00m\x00\x00\x99\xed\x00\x00\x0c\xa6\x00\x00\x00\r"
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x98\x01\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd9\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x00\x00\x00\x00\t-\x00\x00\x00\x01`8\x98\x01\x00\x00\x9a\xd6\x01\xbe\x0b\xd0\x00\x00\x00\x00\x00)\xf3`\x00\x00\x00\x00\x001\xf1o\x00\x00\x99\xd4\x00\x00\x01\x08\x00\x00\x00\x05\x00\x00\x99\xcf\x00\x00\x11!\x00\x00\x00\x0b\x00\x00\x99\xca\x00\x00\x01\x04\x00\x00\x00\x03\x00\x00\x99\xc5\x00\x00\x11\xf4\x00\x00\x00\x11\x00\x00\x99\xc0\x00\x00\x0en\x00\x00\x00\x14\x00\x00\x99\xd9\x00\x00\x00c\x00\x00\x00\x02\x00\x00\x99\xde\x00\x00\x03<\x00\x00\x00\x07\x00\x00\x99\xe3\x00\x00\x12\xc2\x00\x00\x00\x1a\x00\x00\x99\xe8\x00\x00X \x00\x00\x00n\x00\x00\x99\xed\x00\x00\n/\x00\x00\x00\x0b'
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x98\x02\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd9\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x00\x00\x00\x00\x0b\x9a\x00\x00\x00\x05`8\x98\x02\x00\x00\x9a\xd6\x01\xbe\x0e=\x00\x00\x00\x00\x00)\xf8\x8e\x00\x00\x00\x00\x002\tu\x00\x00\x99\xd4\x00\x00\x01\x12\x00\x00\x00\x06\x00\x00\x99\xcf\x00\x00\x15\t\x00\x00\x00\x0c\x00\x00\x99\xca\x00\x00\x01\x7f\x00\x00\x00\x04\x00\x00\x99\xc5\x00\x00\x11\xfe\x00\x00\x00\x12\x00\x00\x99\xc0\x00\x00\x0bN\x00\x00\x00\x13\x00\x00\x99\xd9\x00\x00\x00\x80\x00\x00\x00\x01\x00\x00\x99\xde\x00\x00\x03<\x00\x00\x00\x07\x00\x00\x99\xe3\x00\x00\x18B\x00\x00\x00\x1d\x00\x00\x99\xe8\x00\x00a\x1b\x00\x00\x00w\x00\x00\x99\xed\x00\x00\x0ci\x00\x00\x00\r'
# data = b'\x00\x00\t/\x96\xe9\x93\xe5`8\x98\x03\x1c(\x00\x02\x00\xc8\x01\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x99\xd9\x00\x00\x9c\x0e\x00\x00\x9c\xa4\x00\x00\x98\xc6\x00\x00\x9f\x15\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x99\xd4\x00\x00\x99\xd9\x00\x00\x00\x00\x00\x00\r\xa3\x00\x00\x00\x03`8\x98\x03\x00\x00\x9a\xd6\x01\xbe\x10F\x00\x00\x00\x00\x00)\xfa\xd1\x00\x00\x00\x00\x002\x13t\x00\x00\x99\xd4\x00\x00\x00\x1f\x00\x00\x00\x01\x00\x00\x99\xcf\x00\x00\x15\t\x00\x00\x00\x0c\x00\x00\x99\xca\x00\x00\x01\x7f\x00\x00\x00\x04\x00\x00\x99\xc5\x00\x00\x12\x9e\x00\x00\x00\x14\x00\x00\x99\xc0\x00\x00\n\xb8\x00\x00\x00\x12\x00\x00\x99\xd9\x00\x00\x00\x07\x00\x00\x00\x01\x00\x00\x99\xde\x00\x00\x03<\x00\x00\x00\x07\x00\x00\x99\xe3\x00\x00\x18B\x00\x00\x00\x1d\x00\x00\x99\xe8\x00\x00`\\\x00\x00\x00v\x00\x00\x99\xed\x00\x00\x0b\x9e\x00\x00\x00\x0c'



# >>> first_packet_header = data[0:24]
# >>> first_packet_header_struct = struct.unpack('> Q L H H H 6x', first_packet_header)

# (1210000000500875, 1614235215, 7208, 2, 200)


# >>> first_packet_body = data[24:56]
# >>> first_packet_body_struct = struct.unpack('> 4I 2Q', first_packet_body)
# (100, 21075, 20980, 21120, 89936615199120, 90494960947795)

# ===================================================================================

# >>> second_packet_header = data[56:80]
# >>> second_packet_header_struct = struct.unpack('> Q L H H H 6x', second_packet_header)

# (1210000000500875, 1614235215, 7208, 2, 200)


# >>> second_packet_body = data[80:112]
# >>> second_packet_body_struct = struct.unpack('> 4I 2Q', second_packet_body)
# (100, 21075, 20980, 21120, 89936615199120, 90494960947795)


header_format = "> Q L H H H 6x"
common_7208 = "> 10I Q"
extra_7208 = "> 4I 2Q"
market_pic = "> 3I"
num_of_packets = "> H"

FY_P_NET_BID_ASK = "> 3I"

# get data lenght
header_len = 24
packet_len = 32
idx = 0

print(len(data))

(numPackets,) = struct.unpack(num_of_packets, data[:2])
print(numPackets)

(fyToken, timestamp, fyCode, fyFlag, pktLen) = struct.unpack(header_format, data[:header_len])
print({"fyToken": fyToken, "timestamp": timestamp, "fyCode": fyCode, "fyFlag": fyFlag, "pktLen": pktLen})
data = data[header_len:]

body = struct.unpack(common_7208, data[: 48])
print(body)
data = data[48:]
ltq, ltt, atP, vtt, totBuy, totSell = struct.unpack(extra_7208, data[: packet_len])
print({"ltq": ltq, "ltt": ltt, "atP": atP, "vtt": vtt, "totBuy": totBuy, "totSell": totSell})
data = data[packet_len:]
for i in range(0, 10):
    mp = struct.unpack(market_pic, data[:12])
    print(mp)
    data = data[12:]

print(len(data))
print("")
print("")


