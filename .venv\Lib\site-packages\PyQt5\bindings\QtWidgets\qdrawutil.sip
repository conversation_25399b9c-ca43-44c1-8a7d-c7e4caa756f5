// qdrawutil.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qdrawutil.h>
%End

void qDrawShadeLine(QPainter *p, int x1, int y1, int x2, int y2, const QPalette &pal, bool sunken = true, int lineWidth = 1, int midLineWidth = 0);
void qDrawShadeLine(QPainter *p, const QPoint &p1, const QPoint &p2, const QPalette &pal, bool sunken = true, int lineWidth = 1, int midLineWidth = 0);
void qDrawShadeRect(QPainter *p, int x, int y, int w, int h, const QPalette &pal, bool sunken = false, int lineWidth = 1, int midLineWidth = 0, const QBrush *fill = 0);
void qDrawShadeRect(QPainter *p, const QRect &r, const QPalette &pal, bool sunken = false, int lineWidth = 1, int midLineWidth = 0, const QBrush *fill = 0);
void qDrawShadePanel(QPainter *p, int x, int y, int w, int h, const QPalette &pal, bool sunken = false, int lineWidth = 1, const QBrush *fill = 0);
void qDrawShadePanel(QPainter *p, const QRect &r, const QPalette &pal, bool sunken = false, int lineWidth = 1, const QBrush *fill = 0);
void qDrawWinButton(QPainter *p, int x, int y, int w, int h, const QPalette &pal, bool sunken = false, const QBrush *fill = 0);
void qDrawWinButton(QPainter *p, const QRect &r, const QPalette &pal, bool sunken = false, const QBrush *fill = 0);
void qDrawWinPanel(QPainter *p, int x, int y, int w, int h, const QPalette &pal, bool sunken = false, const QBrush *fill = 0);
void qDrawWinPanel(QPainter *p, const QRect &r, const QPalette &pal, bool sunken = false, const QBrush *fill = 0);
void qDrawPlainRect(QPainter *p, int x, int y, int w, int h, const QColor &, int lineWidth = 1, const QBrush *fill = 0);
void qDrawPlainRect(QPainter *p, const QRect &r, const QColor &, int lineWidth = 1, const QBrush *fill = 0);
void qDrawBorderPixmap(QPainter *painter, const QRect &target, const QMargins &margins, const QPixmap &pixmap);
