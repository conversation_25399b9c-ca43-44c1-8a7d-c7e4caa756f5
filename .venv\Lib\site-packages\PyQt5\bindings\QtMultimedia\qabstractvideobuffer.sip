// qabstractvideobuffer.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractVideoBuffer
{
%TypeHeaderCode
#include <qabstractvideobuffer.h>
%End

public:
    enum HandleType
    {
        NoHandle,
        GLTextureHandle,
        XvShmImageHandle,
        CoreImageHandle,
        QPixmapHandle,
%If (Qt_5_4_0 -)
        EGLImageHandle,
%End
        UserHandle,
    };

    enum MapMode
    {
        NotMapped,
        ReadOnly,
        WriteOnly,
        ReadWrite,
    };

    QAbstractVideoBuffer(QAbstractVideoBuffer::HandleType type);
    virtual ~QAbstractVideoBuffer();
    QAbstractVideoBuffer::HandleType handleType() const;
    virtual QAbstractVideoBuffer::MapMode mapMode() const = 0;
    virtual SIP_PYOBJECT map(QAbstractVideoBuffer::MapMode mode, int *numBytes, int *bytesPerLine) = 0 /TypeHint="PyQt5.sip.voidptr"/ [uchar * (QAbstractVideoBuffer::MapMode mode, int *numBytes, int *bytesPerLine)];
%MethodCode
        uchar *mem;
        
        Py_BEGIN_ALLOW_THREADS
        mem = sipCpp->map(a0, &a1, &a2);
        Py_END_ALLOW_THREADS
        
        if (mem)
        {
            if (a0 & QAbstractVideoBuffer::WriteOnly)
                sipRes = sipConvertFromVoidPtrAndSize(mem, a1);
            else
                sipRes = sipConvertFromConstVoidPtrAndSize(mem, a1);
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End

    virtual void unmap() = 0;
    virtual QVariant handle() const;
    virtual void release();

private:
    QAbstractVideoBuffer(const QAbstractVideoBuffer &);
};
