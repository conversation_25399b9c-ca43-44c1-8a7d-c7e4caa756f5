// qndefnfcsmartposterrecord.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QNdefNfcIconRecord : QNdefRecord
{
%TypeHeaderCode
#include <qndefnfcsmartposterrecord.h>
%End

public:
    QNdefNfcIconRecord();
    QNdefNfcIconRecord(const QNdefRecord &other);
    void setData(const QByteArray &data);
    QByteArray data() const;
};

%End
%If (Qt_5_5_0 -)

class QNdefNfcSmartPosterRecord : QNdefRecord
{
%TypeHeaderCode
#include <qndefnfcsmartposterrecord.h>
%End

public:
    enum Action
    {
        UnspecifiedAction,
        DoAction,
        SaveAction,
        EditAction,
    };

    QNdefNfcSmartPosterRecord();
    QNdefNfcSmartPosterRecord(const QNdefNfcSmartPosterRecord &other);
    QNdefNfcSmartPosterRecord(const QNdefRecord &other);
    ~QNdefNfcSmartPosterRecord();
    void setPayload(const QByteArray &payload);
    bool hasTitle(const QString &locale = QString()) const;
    bool hasAction() const;
    bool hasIcon(const QByteArray &mimetype = QByteArray()) const;
    bool hasSize() const;
    bool hasTypeInfo() const;
    int titleCount() const;
    QString title(const QString &locale = QString()) const;
    QNdefNfcTextRecord titleRecord(const int index) const;
    QList<QNdefNfcTextRecord> titleRecords() const;
    bool addTitle(const QNdefNfcTextRecord &text);
    bool addTitle(const QString &text, const QString &locale, QNdefNfcTextRecord::Encoding encoding);
    bool removeTitle(const QNdefNfcTextRecord &text);
    bool removeTitle(const QString &locale);
    void setTitles(const QList<QNdefNfcTextRecord> &titles);
    QUrl uri() const;
    QNdefNfcUriRecord uriRecord() const;
    void setUri(const QNdefNfcUriRecord &url);
    void setUri(const QUrl &url);
    QNdefNfcSmartPosterRecord::Action action() const;
    void setAction(QNdefNfcSmartPosterRecord::Action act);
    int iconCount() const;
    QByteArray icon(const QByteArray &mimetype = QByteArray()) const;
    QNdefNfcIconRecord iconRecord(const int index) const;
    QList<QNdefNfcIconRecord> iconRecords() const;
    void addIcon(const QNdefNfcIconRecord &icon);
    void addIcon(const QByteArray &type, const QByteArray &data);
    bool removeIcon(const QNdefNfcIconRecord &icon);
    bool removeIcon(const QByteArray &type);
    void setIcons(const QList<QNdefNfcIconRecord> &icons);
    quint32 size() const;
    void setSize(quint32 size);
    QByteArray typeInfo() const;
    void setTypeInfo(const QByteArray &type);
};

%End
