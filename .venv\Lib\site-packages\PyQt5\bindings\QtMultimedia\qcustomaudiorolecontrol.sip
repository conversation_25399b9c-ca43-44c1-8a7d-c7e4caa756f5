// qcustomaudiorolecontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_11_0 -)

class QCustomAudioRoleControl : QMediaControl
{
%TypeHeaderCode
#include <qcustomaudiorolecontrol.h>
%End

public:
    virtual ~QCustomAudioRoleControl();
    virtual QString customAudioRole() const = 0;
    virtual void setCustomAudioRole(const QString &role) = 0;
    virtual QStringList supportedCustomAudioRoles() const = 0;

signals:
    void customAudioRoleChanged(const QString &role);

protected:
    explicit QCustomAudioRoleControl(QObject *parent /TransferThis/ = 0);
};

%End
