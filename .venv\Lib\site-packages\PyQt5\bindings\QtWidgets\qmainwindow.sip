// qmainwindow.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMainWindow : QWidget
{
%TypeHeaderCode
#include <qmainwindow.h>
%End

public:
    QMainWindow(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QMainWindow();
    QSize iconSize() const;
    void setIconSize(const QSize &iconSize);
    Qt::ToolButtonStyle toolButtonStyle() const;
    void setToolButtonStyle(Qt::ToolButtonStyle toolButtonStyle);
    QMenuBar *menuBar() const /Transfer/;
    void setMenuBar(QMenuBar *menubar /Transfer/);
    QStatusBar *statusBar() const /Transfer/;
    void setStatusBar(QStatusBar *statusbar /Transfer/);
    QWidget *centralWidget() const;
    void setCentralWidget(QWidget *widget /Transfer/);
    void setCorner(Qt::Corner corner, Qt::DockWidgetArea area);
    Qt::DockWidgetArea corner(Qt::Corner corner) const;
    void addToolBarBreak(Qt::ToolBarArea area = Qt::TopToolBarArea);
    void insertToolBarBreak(QToolBar *before);
    void addToolBar(Qt::ToolBarArea area, QToolBar *toolbar /Transfer/);
    void addToolBar(QToolBar *toolbar /Transfer/);
    QToolBar *addToolBar(const QString &title) /Transfer/;
    void insertToolBar(QToolBar *before, QToolBar *toolbar /Transfer/);
    void removeToolBar(QToolBar *toolbar);
    Qt::ToolBarArea toolBarArea(QToolBar *toolbar) const;
    void addDockWidget(Qt::DockWidgetArea area, QDockWidget *dockwidget /Transfer/);
    void addDockWidget(Qt::DockWidgetArea area, QDockWidget *dockwidget /Transfer/, Qt::Orientation orientation);
    void splitDockWidget(QDockWidget *after, QDockWidget *dockwidget /Transfer/, Qt::Orientation orientation);
    void removeDockWidget(QDockWidget *dockwidget /TransferBack/);
    Qt::DockWidgetArea dockWidgetArea(QDockWidget *dockwidget) const;
    QByteArray saveState(int version = 0) const;
    bool restoreState(const QByteArray &state, int version = 0);
    virtual QMenu *createPopupMenu();

public slots:
    void setAnimated(bool enabled);
    void setDockNestingEnabled(bool enabled);

signals:
    void iconSizeChanged(const QSize &iconSize);
    void toolButtonStyleChanged(Qt::ToolButtonStyle toolButtonStyle);
%If (Qt_5_8_0 -)
    void tabifiedDockWidgetActivated(QDockWidget *dockWidget);
%End

protected:
    virtual void contextMenuEvent(QContextMenuEvent *event);
    virtual bool event(QEvent *event);

public:
    bool isAnimated() const;
    bool isDockNestingEnabled() const;
    bool isSeparator(const QPoint &pos) const;
    QWidget *menuWidget() const;
    void setMenuWidget(QWidget *menubar /Transfer/);
    void tabifyDockWidget(QDockWidget *first, QDockWidget *second);

    enum DockOption
    {
        AnimatedDocks,
        AllowNestedDocks,
        AllowTabbedDocks,
        ForceTabbedDocks,
        VerticalTabs,
%If (Qt_5_6_0 -)
        GroupedDragging,
%End
    };

    typedef QFlags<QMainWindow::DockOption> DockOptions;
    void setDockOptions(QMainWindow::DockOptions options);
    QMainWindow::DockOptions dockOptions() const;
    void removeToolBarBreak(QToolBar *before);
    bool toolBarBreak(QToolBar *toolbar) const;
%If (Qt_5_2_0 -)
    void setUnifiedTitleAndToolBarOnMac(bool set);
%End
%If (Qt_5_2_0 -)
    bool unifiedTitleAndToolBarOnMac() const;
%End
    bool restoreDockWidget(QDockWidget *dockwidget);
    bool documentMode() const;
    void setDocumentMode(bool enabled);
    QTabWidget::TabShape tabShape() const;
    void setTabShape(QTabWidget::TabShape tabShape);
    QTabWidget::TabPosition tabPosition(Qt::DockWidgetArea area) const;
    void setTabPosition(Qt::DockWidgetAreas areas, QTabWidget::TabPosition tabPosition);
    QList<QDockWidget *> tabifiedDockWidgets(QDockWidget *dockwidget) const;
%If (Qt_5_2_0 -)
    QWidget *takeCentralWidget() /TransferBack/;
%End
%If (Qt_5_6_0 -)
    void resizeDocks(const QList<QDockWidget *> &docks, const QList<int> &sizes, Qt::Orientation orientation);
%End
};

QFlags<QMainWindow::DockOption> operator|(QMainWindow::DockOption f1, QFlags<QMainWindow::DockOption> f2);
