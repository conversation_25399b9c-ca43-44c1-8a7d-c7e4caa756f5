// qquickwindow.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickWindow : QWindow /ExportDerived/
{
%TypeHeaderCode
#include <qquickwindow.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if QT_VERSION >= 0x050400
        {sipName_QSGEngine, &sipType_QSGEngine, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
        {sipName_QQuickItem, &sipType_QQuickItem, 11, 2},
    #if QT_VERSION >= 0x050400
        {sipName_QQuickItemGrabResult, &sipType_QQuickItemGrabResult, -1, 3},
    #else
        {0, 0, -1, 3},
    #endif
        {sipName_QSGTexture, &sipType_QSGTexture, 13, 4},
    #if QT_VERSION >= 0x050100
        {sipName_QQuickTextDocument, &sipType_QQuickTextDocument, -1, 5},
    #else
        {0, 0, -1, 5},
    #endif
    #if QT_VERSION >= 0x050400
        {sipName_QSGAbstractRenderer, &sipType_QSGAbstractRenderer, -1, 6},
    #else
        {0, 0, -1, 6},
    #endif
    #if QT_VERSION >= 0x050600
        {sipName_QQuickImageResponse, &sipType_QQuickImageResponse, -1, 7},
    #else
        {0, 0, -1, 7},
    #endif
        {sipName_QQuickTextureFactory, &sipType_QQuickTextureFactory, -1, 8},
    #if QT_VERSION >= 0x050400
        {sipName_QQuickRenderControl, &sipType_QQuickRenderControl, -1, 9},
    #else
        {0, 0, -1, 9},
    #endif
        {sipName_QSGTextureProvider, &sipType_QSGTextureProvider, -1, 10},
        {sipName_QQuickWindow, &sipType_QQuickWindow, 14, -1},
    #if QT_VERSION >= 0x050200
        {sipName_QQuickFramebufferObject, &sipType_QQuickFramebufferObject, -1, 12},
    #else
        {0, 0, -1, 12},
    #endif
        {sipName_QQuickPaintedItem, &sipType_QQuickPaintedItem, -1, -1},
        {sipName_QSGDynamicTexture, &sipType_QSGDynamicTexture, -1, -1},
        {sipName_QQuickView, &sipType_QQuickView, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum CreateTextureOption
    {
        TextureHasAlphaChannel,
        TextureHasMipmaps,
        TextureOwnsGLTexture,
%If (Qt_5_2_0 -)
        TextureCanUseAtlas,
%End
%If (Qt_5_6_0 -)
        TextureIsOpaque,
%End
    };

    typedef QFlags<QQuickWindow::CreateTextureOption> CreateTextureOptions;
%If (Qt_5_6_1 -)
    explicit QQuickWindow(QWindow *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QQuickWindow(QWindow *parent /TransferThis/ = 0);
%End
    virtual ~QQuickWindow() /ReleaseGIL/;
    QQuickItem *contentItem() const;
    QQuickItem *activeFocusItem() const;
    virtual QObject *focusObject() const;
    QQuickItem *mouseGrabberItem() const;
    bool sendEvent(QQuickItem *, QEvent *);
    QImage grabWindow() /ReleaseGIL/;
%If (PyQt_OpenGL)
    void setRenderTarget(QOpenGLFramebufferObject *fbo);
%End
%If (PyQt_OpenGL)
    QOpenGLFramebufferObject *renderTarget() const;
%End
    void setRenderTarget(uint fboId, const QSize &size);
    uint renderTargetId() const;
    QSize renderTargetSize() const;
    QQmlIncubationController *incubationController() const;
    QSGTexture *createTextureFromImage(const QImage &image) const /Factory/;
%If (Qt_5_2_0 -)
    QSGTexture *createTextureFromImage(const QImage &image, QQuickWindow::CreateTextureOptions options) const /Factory/;
%End
    QSGTexture *createTextureFromId(uint id, const QSize &size, QQuickWindow::CreateTextureOptions options = QQuickWindow::CreateTextureOption()) const /Factory/;
%If (Qt_5_14_0 -)
    QSGTexture *createTextureFromNativeObject(QQuickWindow::NativeObjectType type, const void *nativeObjectPtr, int nativeLayout, const QSize &size, QQuickWindow::CreateTextureOptions options = QQuickWindow::CreateTextureOption()) const /Factory/;
%End
    void setClearBeforeRendering(bool enabled);
    bool clearBeforeRendering() const;
    void setColor(const QColor &color);
    QColor color() const;
    void setPersistentOpenGLContext(bool persistent);
    bool isPersistentOpenGLContext() const;
    void setPersistentSceneGraph(bool persistent);
    bool isPersistentSceneGraph() const;
%If (PyQt_OpenGL)
    QOpenGLContext *openglContext() const;
%End

signals:
    void frameSwapped();
    void sceneGraphInitialized();
    void sceneGraphInvalidated();
    void beforeSynchronizing();
    void beforeRendering();
    void afterRendering();
    void colorChanged(const QColor &);

public slots:
    void update();
    void releaseResources();

protected:
    virtual void exposeEvent(QExposeEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual bool event(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
%If (Qt_5_15_0 -)
    virtual void tabletEvent(QTabletEvent *);
%End

public:
%If (Qt_5_1_0 -)
    static bool hasDefaultAlphaBuffer();
%End
%If (Qt_5_1_0 -)
    static void setDefaultAlphaBuffer(bool useAlpha);
%End

signals:
%If (Qt_5_1_0 -)
    void closing(QQuickCloseEvent *close);
%End
%If (Qt_5_1_0 -)
    void activeFocusItemChanged();
%End

public:
%If (Qt_5_2_0 -)
%If (PyQt_OpenGL)
    void resetOpenGLState();
%End
%End
%If (Qt_5_3_0 -)

    enum SceneGraphError
    {
        ContextNotAvailable,
    };

%End

signals:
%If (Qt_5_3_0 -)
%If (PyQt_OpenGL)
    void openglContextCreated(QOpenGLContext *context);
%End
%End
%If (Qt_5_3_0 -)
    void afterSynchronizing();
%End
%If (Qt_5_3_0 -)
    void afterAnimating();
%End
%If (Qt_5_3_0 -)
    void sceneGraphAboutToStop();
%End
%If (Qt_5_3_0 -)
    void sceneGraphError(QQuickWindow::SceneGraphError error, const QString &message);
%End

public:
%If (Qt_5_4_0 -)

    enum RenderStage
    {
        BeforeSynchronizingStage,
        AfterSynchronizingStage,
        BeforeRenderingStage,
        AfterRenderingStage,
        AfterSwapStage,
%If (Qt_5_6_0 -)
        NoStage,
%End
    };

%End
%If (Qt_5_4_0 -)
    void scheduleRenderJob(QRunnable *job /Transfer/, QQuickWindow::RenderStage schedule) /ReleaseGIL/;
%End
%If (Qt_5_4_0 -)
    qreal effectiveDevicePixelRatio() const;
%End
%If (Qt_5_5_0 -)
    bool isSceneGraphInitialized() const;
%End
%If (Qt_5_8_0 -)
    QSGRendererInterface *rendererInterface() const;
%End
%If (Qt_5_8_0 -)
    static void setSceneGraphBackend(QSGRendererInterface::GraphicsApi api);
%End
%If (Qt_5_8_0 -)
    static void setSceneGraphBackend(const QString &backend);
%End
%If (Qt_5_8_0 -)
    QSGRectangleNode *createRectangleNode() const /Factory/;
%End
%If (Qt_5_8_0 -)
    QSGImageNode *createImageNode() const /Factory/;
%End
%If (Qt_5_9_0 -)
    static QString sceneGraphBackend();
%End
%If (Qt_5_10_0 -)

    enum TextRenderType
    {
        QtTextRendering,
        NativeTextRendering,
    };

%End
%If (Qt_5_10_0 -)
    static QQuickWindow::TextRenderType textRenderType();
%End
%If (Qt_5_10_0 -)
    static void setTextRenderType(QQuickWindow::TextRenderType renderType);
%End
%If (Qt_5_14_0 -)

    enum NativeObjectType
    {
        NativeObjectTexture,
    };

%End
%If (Qt_5_14_0 -)
    void beginExternalCommands();
%End
%If (Qt_5_14_0 -)
    void endExternalCommands();
%End

signals:
%If (Qt_5_14_0 -)
    void beforeRenderPassRecording();
%End
%If (Qt_5_14_0 -)
    void afterRenderPassRecording();
%End
};

%If (Qt_5_1_0 -)
class QQuickCloseEvent;
%End

%ModuleHeaderCode
#include "qpyquick_api.h"
%End

%PostInitialisationCode
qpyquick_post_init();
%End
