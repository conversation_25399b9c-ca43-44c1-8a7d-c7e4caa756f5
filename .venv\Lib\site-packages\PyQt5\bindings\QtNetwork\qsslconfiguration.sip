// qsslconfiguration.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

class QSslConfiguration
{
%TypeHeaderCode
#include <qsslconfiguration.h>
%End

public:
    QSslConfiguration();
    QSslConfiguration(const QSslConfiguration &other);
    ~QSslConfiguration();
    bool isNull() const;
    QSsl::SslProtocol protocol() const;
    void setProtocol(QSsl::SslProtocol protocol);
    QSslSocket::PeerVerifyMode peerVerifyMode() const;
    void setPeerVerifyMode(QSslSocket::PeerVerifyMode mode);
    int peerVerifyDepth() const;
    void setPeerVerifyDepth(int depth);
    QSslCertificate localCertificate() const;
    void setLocalCertificate(const QSslCertificate &certificate);
    QSslCertificate peerCertificate() const;
    QList<QSslCertificate> peerCertificateChain() const;
    QSslCipher sessionCipher() const;
    QSslKey privateKey() const;
    void setPrivateKey(const QSslKey &key);
    QList<QSslCipher> ciphers() const;
    void setCiphers(const QList<QSslCipher> &ciphers);
    QList<QSslCertificate> caCertificates() const;
    void setCaCertificates(const QList<QSslCertificate> &certificates);
    static QSslConfiguration defaultConfiguration();
    static void setDefaultConfiguration(const QSslConfiguration &configuration);
    bool operator==(const QSslConfiguration &other) const;
    bool operator!=(const QSslConfiguration &other) const;
    void setSslOption(QSsl::SslOption option, bool on);
    bool testSslOption(QSsl::SslOption option) const;
    void swap(QSslConfiguration &other /Constrained/);
%If (Qt_5_1_0 -)
    QList<QSslCertificate> localCertificateChain() const;
%End
%If (Qt_5_1_0 -)
    void setLocalCertificateChain(const QList<QSslCertificate> &localChain);
%End
%If (Qt_5_2_0 -)
    QByteArray sessionTicket() const;
%End
%If (Qt_5_2_0 -)
    void setSessionTicket(const QByteArray &sessionTicket);
%End
%If (Qt_5_2_0 -)
    int sessionTicketLifeTimeHint() const;
%End
%If (Qt_5_3_0 -)

    enum NextProtocolNegotiationStatus
    {
        NextProtocolNegotiationNone,
        NextProtocolNegotiationNegotiated,
        NextProtocolNegotiationUnsupported,
    };

%End
%If (Qt_5_3_0 -)
    void setAllowedNextProtocols(QList<QByteArray> protocols);
%End
%If (Qt_5_3_0 -)
    QList<QByteArray> allowedNextProtocols() const;
%End
%If (Qt_5_3_0 -)
    QByteArray nextNegotiatedProtocol() const;
%End
%If (Qt_5_3_0 -)
    QSslConfiguration::NextProtocolNegotiationStatus nextProtocolNegotiationStatus() const;
%End
%If (Qt_5_3_0 -)
    static const char *NextProtocolSpdy3_0 /Encoding="None",NoSetter/;
%End
%If (Qt_5_3_0 -)
    static const char *NextProtocolHttp1_1 /Encoding="None",NoSetter/;
%End
%If (Qt_5_4_0 -)
    QSsl::SslProtocol sessionProtocol() const;
%End
%If (Qt_5_5_0 -)
    static QList<QSslCipher> supportedCiphers();
%End
%If (Qt_5_5_0 -)
    static QList<QSslCertificate> systemCaCertificates();
%End
%If (Qt_5_5_0 -)
    QVector<QSslEllipticCurve> ellipticCurves() const;
%End
%If (Qt_5_5_0 -)
    void setEllipticCurves(const QVector<QSslEllipticCurve> &curves);
%End
%If (Qt_5_5_0 -)
    static QVector<QSslEllipticCurve> supportedEllipticCurves();
%End
%If (Qt_5_7_0 -)
    QSslKey ephemeralServerKey() const;
%End
%If (Qt_5_8_0 -)
    QByteArray preSharedKeyIdentityHint() const;
%End
%If (Qt_5_8_0 -)
    void setPreSharedKeyIdentityHint(const QByteArray &hint);
%End
%If (Qt_5_8_0 -)
    QSslDiffieHellmanParameters diffieHellmanParameters() const;
%End
%If (Qt_5_8_0 -)
    void setDiffieHellmanParameters(const QSslDiffieHellmanParameters &dhparams);
%End
%If (Qt_5_11_0 -)
    QMap<QByteArray, QVariant> backendConfiguration() const;
%End
%If (Qt_5_11_0 -)
    void setBackendConfigurationOption(const QByteArray &name, const QVariant &value);
%End
%If (Qt_5_11_0 -)
    void setBackendConfiguration(const QMap<QByteArray, QVariant> &backendConfiguration = QMap<QByteArray, QVariant>());
%End
%If (Qt_5_13_0 -)
    void setOcspStaplingEnabled(bool enable);
%End
%If (Qt_5_13_0 -)
    bool ocspStaplingEnabled() const;
%End
%If (Qt_5_15_0 -)
    void addCaCertificate(const QSslCertificate &certificate);
%End
%If (Qt_5_15_0 -)
    bool addCaCertificates(const QString &path, QSsl::EncodingFormat format = QSsl::Pem, QSslCertificate::PatternSyntax syntax = QSslCertificate::PatternSyntax::FixedString);
%End
%If (Qt_5_15_0 -)
    void addCaCertificates(const QList<QSslCertificate> &certificates);
%End
};

%End
