import os

# Change this to your main directory path
thursday_output_folder = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting\Thursday_output_folder"

# Iterate over each subfolder in the Thursday_output_folder
for subfolder in os.listdir(thursday_output_folder):
    subfolder_path = os.path.join(thursday_output_folder, subfolder)
    
    # Ensure it's a directory
    if os.path.isdir(subfolder_path):
        # Walk through all subfolders and files in the current subfolder
        for root, dirs, files in os.walk(subfolder_path):
            for file in files:
                if file.endswith(".csv"):
                    file_path = os.path.join(root, file)

                    # Read current contents of the file
                    with open(file_path, "r", newline='') as f:
                        content = f.read()
                        
                    # Check if headers already exist
                    if not content.startswith("Date (YYYYMMDD),Time(HH: MM),Open,High,Low,Close,Volume,Open Interest\n"):
                        # Add the header line exactly as it should appear
                        header_line = "YMD (YYYYMMDD),Time(HH: MM),Open,High,Low,Close,Volume,Open Interest\n"       
                                    
                        # Write headers and original content
                        with open(file_path, "w", newline='') as f:
                            f.write(header_line + content)

print("✅ Headers added to all CSV files in each subfolder.")