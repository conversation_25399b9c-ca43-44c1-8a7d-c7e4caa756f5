../../Scripts/wsdump.exe,sha256=YSs78JUDosMR1cB1fow9GjTmfMrLPFwgpE6pS75NP9k,108435
websocket/__init__.py,sha256=998bG4YArUwopLjWQt5svK7WMyKe7mHNU7-APJphvXA,801
websocket/__pycache__/__init__.cpython-312.pyc,,
websocket/__pycache__/_abnf.cpython-312.pyc,,
websocket/__pycache__/_app.cpython-312.pyc,,
websocket/__pycache__/_cookiejar.cpython-312.pyc,,
websocket/__pycache__/_core.cpython-312.pyc,,
websocket/__pycache__/_exceptions.cpython-312.pyc,,
websocket/__pycache__/_handshake.cpython-312.pyc,,
websocket/__pycache__/_http.cpython-312.pyc,,
websocket/__pycache__/_logging.cpython-312.pyc,,
websocket/__pycache__/_socket.cpython-312.pyc,,
websocket/__pycache__/_ssl_compat.cpython-312.pyc,,
websocket/__pycache__/_url.cpython-312.pyc,,
websocket/__pycache__/_utils.cpython-312.pyc,,
websocket/__pycache__/_wsdump.cpython-312.pyc,,
websocket/_abnf.py,sha256=7TaWp3eWHNm6FGo_JHjuvEM33n-SDOwBWt3WW1HRc-8,13656
websocket/_app.py,sha256=YRb_KBghOLd-OeDWVTkn5YjQNVwlKxj_DAPJ-TysKzQ,21207
websocket/_cookiejar.py,sha256=FDj1put7LjUXwvz84GJST2CcC8CLHh55b4GTPGtZQAU,2164
websocket/_core.py,sha256=rnplVpKe11GSlXe-S3YosmVmW0BpnUxPSndlPdbYxQw,20110
websocket/_exceptions.py,sha256=2mkZ71UqUbNQB9iHtzI4epWdgyUOC8IqlddNzd7Pzaw,2116
websocket/_handshake.py,sha256=9XtBHf1S5tDKcQzeuU-aPDD2svvsuv1T6EtmMnRCDVc,6709
websocket/_http.py,sha256=2WpdQJiJKs_xw0Tjo4Gz0t4Z1s1mxR4d9CsBfVxkkZ0,11816
websocket/_logging.py,sha256=YREo1FZT9Jnm0ub6AFDFnfq_2jfN380Jvm1crHSi_tQ,2220
websocket/_socket.py,sha256=62kZCvJlseCV2AS2eOeMUV4s4ngAex--0lkbhG8I-e4,5000
websocket/_ssl_compat.py,sha256=yORctq04HZ2KL_Q6HlLE2FbyWkEmB08lQFNHGpAsOCw,1122
websocket/_url.py,sha256=-SFhW0-I2VCf2FoGst0VS1JrI1To16WsCuVQyhs4StU,4917
websocket/_utils.py,sha256=15GM18HB2wOzQwMYo-OcL0IigroyGEZb67OyRYXOOeQ,3667
websocket/_wsdump.py,sha256=5JdueBJNdSsgSGb0DtgYAhrpeY0GgxdSlgN5ckTkUew,7106
websocket/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websocket/tests/__pycache__/__init__.cpython-312.pyc,,
websocket/tests/__pycache__/echo-server.cpython-312.pyc,,
websocket/tests/__pycache__/test_abnf.cpython-312.pyc,,
websocket/tests/__pycache__/test_app.cpython-312.pyc,,
websocket/tests/__pycache__/test_cookiejar.cpython-312.pyc,,
websocket/tests/__pycache__/test_http.cpython-312.pyc,,
websocket/tests/__pycache__/test_url.cpython-312.pyc,,
websocket/tests/__pycache__/test_websocket.cpython-312.pyc,,
websocket/tests/data/header01.txt,sha256=eR9UDpnf7mREys9MttKytzB5OXA5IwOGWJZKmaF4II8,163
websocket/tests/data/header02.txt,sha256=1HzQGIMG0OGwfnboRkUqwbTEg2nTevOX2Waj0gQARaw,161
websocket/tests/data/header03.txt,sha256=l_soTbfEWjZTLC5Ydx2U8uj8NJ30IwAc8yo5IUG5fyQ,216
websocket/tests/echo-server.py,sha256=04lDXa3sC6AHUpuNaqRfABD4H-wcgExflZTea9VtnYM,486
websocket/tests/test_abnf.py,sha256=Nn2ULwWg4n9Od5SDUkSSIXmrZ2kJ0V_kgP0JdTgUO1A,4175
websocket/tests/test_app.py,sha256=ieKB1veLsN9oGOmGHGT0Uxqpz10N5kn5oHluPGAM_3U,12681
websocket/tests/test_cookiejar.py,sha256=juNAocVF7MMvRdpy4AndioYJbR2WwjVpaqaoYsUiSV0,4325
websocket/tests/test_http.py,sha256=2ECktme_bnxUGwfbOyCTv02hIjvPxtcGx3PIp5EpOH8,9623
websocket/tests/test_url.py,sha256=kFPYOJD__GaM6gisGTjQbrEWDpCy0ThTzOF7-bvmBMw,14589
websocket/tests/test_websocket.py,sha256=Qj1MOaQexcXzghIHHYa2X22ilNdqAKQ0Aws9x-1mdCI,18072
websocket_client-1.6.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
websocket_client-1.6.1.dist-info/LICENSE,sha256=Sb7oRCI-MIhpjcBBwnuSektU2hvNPp6Cv7fU9pUU3JU,11339
websocket_client-1.6.1.dist-info/METADATA,sha256=PO3AtoD__kR5rpMlA2oavqS9Pwbcs6CA52MUdJ8w8AE,7595
websocket_client-1.6.1.dist-info/RECORD,,
websocket_client-1.6.1.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
websocket_client-1.6.1.dist-info/entry_points.txt,sha256=IoCGCuANLuLxE3m2QXEd2Ip57qScBjf7RfQnkjn6DNE,50
websocket_client-1.6.1.dist-info/top_level.txt,sha256=8m_tTpcUlzWGl8v-pj5Wi7XhAFaN1_bLKRHQKCyz5_I,10
